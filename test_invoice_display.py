#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لنافذة الفاتورة المحسنة
"""

import sys
import os
sys.path.append('src')

def test_invoice_window():
    """اختبار نافذة الفاتورة"""
    print("🧪 اختبار نافذة الفاتورة المحسنة...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from purchases_window import InvoiceDialog
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء نافذة الفاتورة
        dialog = InvoiceDialog()
        
        print("✅ تم إنشاء نافذة الفاتورة")
        print(f"📏 حجم النافذة: {dialog.width()}×{dialog.height()}")
        
        # عرض النافذة
        dialog.show()
        
        print("✅ تم عرض النافذة")
        print("\n📋 تحقق من:")
        print("   ✅ ظهور جميع الخانات والحقول")
        print("   ✅ وضوح النصوص والتسميات")
        print("   ✅ ظهور أزرار الحفظ والإلغاء")
        print("   ✅ رقم الفاتورة التلقائي")
        print("   ✅ العملة بالدينار الجزائري (دج)")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النافذة: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_invoice_window()
