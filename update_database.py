#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت تحديث قاعدة البيانات
إضافة الحقول الجديدة لجدول فواتير المشتريات
"""

import sys
import os
sys.path.append('src')

from database import db

def update_database():
    """تحديث قاعدة البيانات بإضافة الحقول الجديدة"""
    try:
        print("🔄 بدء تحديث قاعدة البيانات...")
        
        # التحقق من وجود الحقول الجديدة
        columns_info = db.execute_query("PRAGMA table_info(purchase_invoices)")
        existing_columns = [col['name'] for col in columns_info]
        
        print(f"📋 الحقول الموجودة: {existing_columns}")
        
        # إضافة حقل الحالة إذا لم يكن موجوداً
        if 'status' not in existing_columns:
            print("➕ إضافة حقل الحالة...")
            db.execute_query("ALTER TABLE purchase_invoices ADD COLUMN status TEXT DEFAULT 'مسودة'")
            print("✅ تم إضافة حقل الحالة")
        else:
            print("ℹ️ حقل الحالة موجود بالفعل")
        
        # إضافة حقل طريقة الدفع إذا لم يكن موجوداً
        if 'payment_method' not in existing_columns:
            print("➕ إضافة حقل طريقة الدفع...")
            db.execute_query("ALTER TABLE purchase_invoices ADD COLUMN payment_method TEXT DEFAULT 'نقداً'")
            print("✅ تم إضافة حقل طريقة الدفع")
        else:
            print("ℹ️ حقل طريقة الدفع موجود بالفعل")
        
        # تحديث الفواتير الموجودة بالقيم الافتراضية
        print("🔄 تحديث الفواتير الموجودة...")
        
        # تحديث الحالة للفواتير التي لا تحتوي على حالة
        db.execute_query("UPDATE purchase_invoices SET status = 'مسودة' WHERE status IS NULL OR status = ''")
        
        # تحديث طريقة الدفع للفواتير التي لا تحتوي على طريقة دفع
        db.execute_query("UPDATE purchase_invoices SET payment_method = 'نقداً' WHERE payment_method IS NULL OR payment_method = ''")
        
        # التحقق من النتائج
        updated_columns = db.execute_query("PRAGMA table_info(purchase_invoices)")
        print(f"📋 الحقول بعد التحديث: {[col['name'] for col in updated_columns]}")
        
        # عرض عدد الفواتير المحدثة
        invoice_count = db.execute_query("SELECT COUNT(*) as count FROM purchase_invoices")[0]['count']
        print(f"📊 عدد الفواتير في قاعدة البيانات: {invoice_count}")
        
        print("✅ تم تحديث قاعدة البيانات بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث قاعدة البيانات: {str(e)}")
        return False

def test_database():
    """اختبار قاعدة البيانات المحدثة"""
    try:
        print("\n🧪 اختبار قاعدة البيانات المحدثة...")
        
        # اختبار إدراج فاتورة جديدة
        test_query = """
            INSERT INTO purchase_invoices 
            (supplier_id, invoice_number, total_amount, paid_amount, invoice_date, status, payment_method, notes)
            VALUES (1, 'TEST-2025-0001', 100.0, 50.0, '2025-06-18', 'مؤكدة', 'شيك', 'فاتورة اختبار')
        """
        
        # التحقق من وجود مورد للاختبار
        suppliers = db.execute_query("SELECT COUNT(*) as count FROM suppliers")
        if suppliers[0]['count'] == 0:
            print("⚠️ لا توجد موردين للاختبار، سيتم إنشاء مورد تجريبي...")
            db.execute_query("INSERT INTO suppliers (name, phone, address) VALUES ('مورد تجريبي', '0123456789', 'عنوان تجريبي')")
        
        # إدراج فاتورة اختبار
        test_id = db.execute_insert(test_query)
        print(f"✅ تم إدراج فاتورة اختبار برقم: {test_id}")
        
        # استرجاع الفاتورة للتأكد
        test_invoice = db.execute_query("SELECT * FROM purchase_invoices WHERE id = ?", (test_id,))
        if test_invoice:
            invoice = test_invoice[0]
            print(f"📋 تفاصيل الفاتورة:")
            print(f"   - الرقم: {invoice['id']}")
            print(f"   - رقم الفاتورة: {invoice['invoice_number']}")
            print(f"   - الحالة: {invoice['status']}")
            print(f"   - طريقة الدفع: {invoice['payment_method']}")
            print(f"   - المبلغ الإجمالي: {invoice['total_amount']} دج")
            print(f"   - المبلغ المدفوع: {invoice['paid_amount']} دج")
        
        # حذف فاتورة الاختبار
        db.execute_query("DELETE FROM purchase_invoices WHERE id = ?", (test_id,))
        print("🗑️ تم حذف فاتورة الاختبار")
        
        print("✅ اختبار قاعدة البيانات مكتمل بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {str(e)}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🔧 سكريبت تحديث قاعدة البيانات")
    print("=" * 60)
    
    # تحديث قاعدة البيانات
    if update_database():
        # اختبار قاعدة البيانات
        if test_database():
            print("\n🎉 تم تحديث واختبار قاعدة البيانات بنجاح!")
            print("💡 يمكنك الآن تشغيل البرنامج الرئيسي")
        else:
            print("\n⚠️ تم التحديث ولكن فشل الاختبار")
    else:
        print("\n❌ فشل في تحديث قاعدة البيانات")
    
    print("=" * 60)
