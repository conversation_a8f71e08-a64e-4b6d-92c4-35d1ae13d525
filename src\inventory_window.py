#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة المخازن
إدارة المواد الأولية والمنتجات النهائية وأدوات التغليف
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QGridLayout, QPushButton, QLabel, QFrame, 
                             QTableWidget, QTableWidgetItem, QLineEdit, 
                             QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                             QMessageBox, QDialog, QFormLayout, QDialogButtonBox,
                             QTabWidget, QHeaderView, QDateEdit, QGroupBox,
                             QCheckBox)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont, QIcon
from database import db

class InventoryWindow(QMainWindow):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إدارة المخازن")

        # تحديد أبعاد مناسبة للشاشات المتوسطة
        self.setFixedSize(1200, 650)

        # توسيط النافذة
        self.center_window()
        
        # إعداد التصميم
        self.setup_style()
        
        # إنشاء الواجهة
        self.setup_ui()
        
        # تحديث البيانات
        self.refresh_data()

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        from PyQt5.QtWidgets import QApplication
        screen = QApplication.desktop().screenGeometry()
        window = self.frameGeometry()

        # حساب الموقع المركزي
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2

        # التأكد من أن النافذة لا تخرج عن حدود الشاشة
        x = max(0, min(x, screen.width() - window.width()))
        y = max(0, min(y, screen.height() - window.height()))

        self.move(x, y)
    
    def setup_style(self):
        """إعداد تصميم النافذة"""
        self.colors = {
            'primary': '#2C3E50',
            'secondary': '#3498DB', 
            'accent': '#E74C3C',
            'success': '#27AE60',
            'warning': '#F39C12',
            'light': '#ECF0F1',
            'dark': '#34495E',
            'white': '#FFFFFF'
        }
        
        self.setStyleSheet(f"""
            QMainWindow {{
                background: {self.colors['light']};
            }}
            
            QTabWidget::pane {{
                border: 2px solid {self.colors['primary']};
                border-radius: 10px;
                background: {self.colors['white']};
            }}
            
            QTabBar::tab {{
                background: {self.colors['secondary']};
                color: white;
                padding: 10px 20px;
                margin: 2px;
                border-radius: 5px;
                font-weight: bold;
                font-family: 'Tahoma';
            }}
            
            QTabBar::tab:selected {{
                background: {self.colors['primary']};
            }}
            
            QPushButton {{
                background: {self.colors['secondary']};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
                font-family: 'Tahoma';
                min-height: 35px;
            }}
            
            QPushButton:hover {{
                background: {self.colors['accent']};
            }}
            
            QPushButton:pressed {{
                background: {self.colors['dark']};
            }}
            
            QTableWidget {{
                background: {self.colors['white']};
                border: 2px solid {self.colors['light']};
                border-radius: 8px;
                gridline-color: {self.colors['light']};
                font-family: 'Tahoma';
            }}
            
            QTableWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {self.colors['light']};
            }}
            
            QTableWidget::item:selected {{
                background: {self.colors['secondary']};
                color: white;
            }}
            
            QHeaderView::section {{
                background: {self.colors['primary']};
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
                font-family: 'Tahoma';
            }}
            
            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {{
                border: 2px solid {self.colors['light']};
                border-radius: 5px;
                padding: 8px;
                font-family: 'Tahoma';
                background: {self.colors['white']};
            }}
            
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {{
                border-color: {self.colors['secondary']};
            }}
            
            QGroupBox {{
                font-weight: bold;
                font-family: 'Tahoma';
                border: 2px solid {self.colors['light']};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                color: {self.colors['primary']};
            }}
        """)
    
    def setup_ui(self):
        """إنشاء واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # إنشاء الهيدر
        header = self.create_header()
        main_layout.addWidget(header)
        
        # إنشاء التبويبات
        tabs = QTabWidget()
        
        # تبويب المواد الأولية
        materials_tab = self.create_materials_tab()
        tabs.addTab(materials_tab, "المواد الأولية")
        
        # تبويب المنتجات النهائية
        products_tab = self.create_products_tab()
        tabs.addTab(products_tab, "المنتجات النهائية")
        
        # تبويب أدوات التغليف
        packaging_tab = self.create_packaging_tab()
        tabs.addTab(packaging_tab, "أدوات التغليف")
        
        # تبويب التنبيهات
        alerts_tab = self.create_alerts_tab()
        tabs.addTab(alerts_tab, "التنبيهات")
        
        main_layout.addWidget(tabs)
        
        # إنشاء أزرار التحكم
        control_buttons = self.create_control_buttons()
        main_layout.addWidget(control_buttons)
    
    def create_header(self):
        """إنشاء منطقة الهيدر"""
        header_frame = QFrame()
        header_frame.setFixedHeight(80)
        header_frame.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {self.colors['primary']}, stop:1 {self.colors['secondary']});
                border-radius: 15px;
            }}
        """)
        
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(20, 10, 20, 10)
        
        # أيقونة ونص الهيدر
        icon_label = QLabel("📦")
        icon_label.setFont(QFont("Arial", 32))
        icon_label.setStyleSheet("color: white; background: transparent;")
        
        title_label = QLabel("إدارة المخازن")
        title_label.setFont(QFont("Tahoma", 20, QFont.Bold))
        title_label.setStyleSheet("color: white; background: transparent;")
        
        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        return header_frame
    
    def create_materials_tab(self):
        """إنشاء تبويب المواد الأولية"""
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        add_stock_btn = QPushButton("إضافة رصيد")
        add_stock_btn.clicked.connect(self.add_material_stock)
        add_stock_btn.setStyleSheet(f"background: {self.colors['success']};")
        
        adjust_stock_btn = QPushButton("تعديل الرصيد")
        adjust_stock_btn.clicked.connect(self.adjust_material_stock)
        adjust_stock_btn.setStyleSheet(f"background: {self.colors['warning']};")
        
        view_movements_btn = QPushButton("عرض الحركات")
        view_movements_btn.clicked.connect(self.view_material_movements)
        
        buttons_layout.addWidget(add_stock_btn)
        buttons_layout.addWidget(adjust_stock_btn)
        buttons_layout.addWidget(view_movements_btn)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        
        # جدول المواد الأولية
        self.materials_table = QTableWidget()
        self.materials_table.setColumnCount(6)
        self.materials_table.setHorizontalHeaderLabels([
            "اسم المادة", "الكمية المتوفرة", "الوحدة", "الحد الأدنى", "الحالة", "آخر تحديث"
        ])
        
        # تعديل عرض الأعمدة
        header = self.materials_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
        
        layout.addWidget(self.materials_table)
        
        return tab_widget
    
    def create_products_tab(self):
        """إنشاء تبويب المنتجات النهائية"""
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        add_production_btn = QPushButton("إضافة إنتاج")
        add_production_btn.clicked.connect(self.add_product_stock)
        add_production_btn.setStyleSheet(f"background: {self.colors['success']};")
        
        sell_products_btn = QPushButton("تسجيل مبيعات")
        sell_products_btn.clicked.connect(self.sell_products)
        sell_products_btn.setStyleSheet(f"background: {self.colors['warning']};")
        
        adjust_products_btn = QPushButton("تعديل الرصيد")
        adjust_products_btn.clicked.connect(self.adjust_product_stock)
        
        buttons_layout.addWidget(add_production_btn)
        buttons_layout.addWidget(sell_products_btn)
        buttons_layout.addWidget(adjust_products_btn)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        
        # جدول المنتجات النهائية
        self.products_table = QTableWidget()
        self.products_table.setColumnCount(5)
        self.products_table.setHorizontalHeaderLabels([
            "اسم المنتج", "الكمية المتوفرة", "الوحدة", "السعر", "القيمة الإجمالية"
        ])
        
        # تعديل عرض الأعمدة
        header = self.products_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        
        layout.addWidget(self.products_table)
        
        return tab_widget

    def create_packaging_tab(self):
        """إنشاء تبويب أدوات التغليف"""
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        add_packaging_btn = QPushButton("إضافة أدوات تغليف")
        add_packaging_btn.clicked.connect(self.add_packaging_tools)
        add_packaging_btn.setStyleSheet(f"background: {self.colors['success']};")

        out_packaging_btn = QPushButton("خروج أدوات")
        out_packaging_btn.clicked.connect(self.packaging_out)
        out_packaging_btn.setStyleSheet(f"background: {self.colors['warning']};")

        in_packaging_btn = QPushButton("إرجاع أدوات")
        in_packaging_btn.clicked.connect(self.packaging_in)
        in_packaging_btn.setStyleSheet(f"background: {self.colors['secondary']};")

        buttons_layout.addWidget(add_packaging_btn)
        buttons_layout.addWidget(out_packaging_btn)
        buttons_layout.addWidget(in_packaging_btn)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        # جدول أدوات التغليف
        self.packaging_table = QTableWidget()
        self.packaging_table.setColumnCount(5)
        self.packaging_table.setHorizontalHeaderLabels([
            "اسم الأداة", "الكمية الإجمالية", "مع الزبائن", "المتوفر", "قابل للإرجاع"
        ])

        # تعديل عرض الأعمدة
        header = self.packaging_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)

        layout.addWidget(self.packaging_table)

        return tab_widget

    def create_alerts_tab(self):
        """إنشاء تبويب التنبيهات"""
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)

        # منطقة الإحصائيات
        stats_group = QGroupBox("إحصائيات المخزن")
        stats_layout = QGridLayout(stats_group)

        self.low_stock_label = QLabel("المواد المنخفضة: 0")
        self.low_stock_label.setFont(QFont("Tahoma", 12, QFont.Bold))
        self.low_stock_label.setStyleSheet(f"color: {self.colors['accent']};")
        stats_layout.addWidget(self.low_stock_label, 0, 0)

        self.out_of_stock_label = QLabel("المواد المنتهية: 0")
        self.out_of_stock_label.setFont(QFont("Tahoma", 12, QFont.Bold))
        self.out_of_stock_label.setStyleSheet(f"color: {self.colors['accent']};")
        stats_layout.addWidget(self.out_of_stock_label, 0, 1)

        self.total_materials_label = QLabel("إجمالي المواد: 0")
        self.total_materials_label.setFont(QFont("Tahoma", 12, QFont.Bold))
        self.total_materials_label.setStyleSheet(f"color: {self.colors['primary']};")
        stats_layout.addWidget(self.total_materials_label, 1, 0)

        self.total_products_label = QLabel("إجمالي المنتجات: 0")
        self.total_products_label.setFont(QFont("Tahoma", 12, QFont.Bold))
        self.total_products_label.setStyleSheet(f"color: {self.colors['primary']};")
        stats_layout.addWidget(self.total_products_label, 1, 1)

        layout.addWidget(stats_group)

        # جدول التنبيهات
        alerts_group = QGroupBox("تنبيهات المخزن")
        alerts_layout = QVBoxLayout(alerts_group)

        self.alerts_table = QTableWidget()
        self.alerts_table.setColumnCount(4)
        self.alerts_table.setHorizontalHeaderLabels([
            "نوع التنبيه", "اسم المادة/المنتج", "الكمية الحالية", "الحد الأدنى"
        ])

        # تعديل عرض الأعمدة
        header = self.alerts_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)

        alerts_layout.addWidget(self.alerts_table)

        layout.addWidget(alerts_group)

        return tab_widget

    def create_control_buttons(self):
        """إنشاء أزرار التحكم السفلية"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)

        refresh_btn = QPushButton("تحديث البيانات")
        refresh_btn.clicked.connect(self.refresh_data)
        refresh_btn.setStyleSheet(f"background: {self.colors['success']};")

        export_inventory_btn = QPushButton("تصدير تقرير المخزن")
        export_inventory_btn.clicked.connect(self.export_inventory_report)

        back_btn = QPushButton("العودة للقائمة الرئيسية")
        back_btn.clicked.connect(self.close)
        back_btn.setStyleSheet(f"background: {self.colors['dark']};")

        buttons_layout.addStretch()
        buttons_layout.addWidget(refresh_btn)
        buttons_layout.addWidget(export_inventory_btn)
        buttons_layout.addWidget(back_btn)

        return buttons_frame

    def refresh_data(self):
        """تحديث جميع البيانات"""
        self.load_materials()
        self.load_products()
        self.load_packaging_tools()
        self.load_alerts()
        self.update_stats()

    def load_materials(self):
        """تحميل بيانات المواد الأولية"""
        try:
            materials = db.execute_query("SELECT * FROM raw_materials ORDER BY name")

            self.materials_table.setRowCount(len(materials))

            for row, material in enumerate(materials):
                stock_status = "طبيعي"
                if material['stock_quantity'] <= 0:
                    stock_status = "منتهي"
                elif material['stock_quantity'] <= material['min_stock']:
                    stock_status = "منخفض"

                self.materials_table.setItem(row, 0, QTableWidgetItem(material['name'] or ''))
                self.materials_table.setItem(row, 1, QTableWidgetItem(f"{material['stock_quantity']:.2f}"))
                self.materials_table.setItem(row, 2, QTableWidgetItem(material['unit'] or ''))
                self.materials_table.setItem(row, 3, QTableWidgetItem(f"{material['min_stock']:.2f}"))
                self.materials_table.setItem(row, 4, QTableWidgetItem(stock_status))
                self.materials_table.setItem(row, 5, QTableWidgetItem("اليوم"))  # يمكن تحسينها لاحقاً

                # تلوين الصفوف حسب الحالة
                if stock_status == "منتهي":
                    for col in range(6):
                        item = self.materials_table.item(row, col)
                        if item:
                            item.setBackground(Qt.red)
                            item.setForeground(Qt.white)
                elif stock_status == "منخفض":
                    for col in range(6):
                        item = self.materials_table.item(row, col)
                        if item:
                            item.setBackground(Qt.yellow)
                            item.setForeground(Qt.black)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات المواد الأولية: {str(e)}")

    def load_products(self):
        """تحميل بيانات المنتجات النهائية"""
        try:
            products = db.execute_query("SELECT * FROM products ORDER BY name")

            self.products_table.setRowCount(len(products))

            for row, product in enumerate(products):
                total_value = product['stock_quantity'] * product['price']

                self.products_table.setItem(row, 0, QTableWidgetItem(product['name'] or ''))
                self.products_table.setItem(row, 1, QTableWidgetItem(f"{product['stock_quantity']:.2f}"))
                self.products_table.setItem(row, 2, QTableWidgetItem("كيلوغرام"))  # افتراضي
                self.products_table.setItem(row, 3, QTableWidgetItem(f"{product['price']:.2f}"))
                self.products_table.setItem(row, 4, QTableWidgetItem(f"{total_value:.2f}"))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات المنتجات: {str(e)}")

    def load_packaging_tools(self):
        """تحميل بيانات أدوات التغليف"""
        try:
            tools = db.execute_query("SELECT * FROM packaging_tools ORDER BY name")

            self.packaging_table.setRowCount(len(tools))

            for row, tool in enumerate(tools):
                returnable = "نعم" if tool['is_returnable'] else "لا"

                self.packaging_table.setItem(row, 0, QTableWidgetItem(tool['name'] or ''))
                self.packaging_table.setItem(row, 1, QTableWidgetItem(str(tool['total_quantity'])))
                self.packaging_table.setItem(row, 2, QTableWidgetItem(str(tool['with_customers'])))
                self.packaging_table.setItem(row, 3, QTableWidgetItem(str(tool['available_quantity'])))
                self.packaging_table.setItem(row, 4, QTableWidgetItem(returnable))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات أدوات التغليف: {str(e)}")

    def load_alerts(self):
        """تحميل التنبيهات"""
        try:
            # تنبيهات المواد المنخفضة
            low_materials = db.execute_query("""
                SELECT name, stock_quantity, min_stock, unit
                FROM raw_materials
                WHERE stock_quantity <= min_stock AND stock_quantity > 0
                ORDER BY (stock_quantity / min_stock)
            """)

            # تنبيهات المواد المنتهية
            out_materials = db.execute_query("""
                SELECT name, stock_quantity, min_stock, unit
                FROM raw_materials
                WHERE stock_quantity <= 0
            """)

            total_alerts = len(low_materials) + len(out_materials)
            self.alerts_table.setRowCount(total_alerts)

            row = 0

            # إضافة تنبيهات المواد المنتهية
            for material in out_materials:
                self.alerts_table.setItem(row, 0, QTableWidgetItem("مادة منتهية"))
                self.alerts_table.setItem(row, 1, QTableWidgetItem(material['name']))
                self.alerts_table.setItem(row, 2, QTableWidgetItem(f"{material['stock_quantity']:.2f} {material['unit']}"))
                self.alerts_table.setItem(row, 3, QTableWidgetItem(f"{material['min_stock']:.2f} {material['unit']}"))

                # تلوين أحمر للمواد المنتهية
                for col in range(4):
                    item = self.alerts_table.item(row, col)
                    if item:
                        item.setBackground(Qt.red)
                        item.setForeground(Qt.white)

                row += 1

            # إضافة تنبيهات المواد المنخفضة
            for material in low_materials:
                self.alerts_table.setItem(row, 0, QTableWidgetItem("مادة منخفضة"))
                self.alerts_table.setItem(row, 1, QTableWidgetItem(material['name']))
                self.alerts_table.setItem(row, 2, QTableWidgetItem(f"{material['stock_quantity']:.2f} {material['unit']}"))
                self.alerts_table.setItem(row, 3, QTableWidgetItem(f"{material['min_stock']:.2f} {material['unit']}"))

                # تلوين أصفر للمواد المنخفضة
                for col in range(4):
                    item = self.alerts_table.item(row, col)
                    if item:
                        item.setBackground(Qt.yellow)
                        item.setForeground(Qt.black)

                row += 1

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل التنبيهات: {str(e)}")

    def update_stats(self):
        """تحديث الإحصائيات"""
        try:
            # إحصائيات المواد
            low_stock_count = db.execute_query("""
                SELECT COUNT(*) as count FROM raw_materials
                WHERE stock_quantity <= min_stock AND stock_quantity > 0
            """)[0]['count']

            out_of_stock_count = db.execute_query("""
                SELECT COUNT(*) as count FROM raw_materials
                WHERE stock_quantity <= 0
            """)[0]['count']

            total_materials_count = db.execute_query("""
                SELECT COUNT(*) as count FROM raw_materials
            """)[0]['count']

            total_products_count = db.execute_query("""
                SELECT COUNT(*) as count FROM products
            """)[0]['count']

            # تحديث التسميات
            self.low_stock_label.setText(f"المواد المنخفضة: {low_stock_count}")
            self.out_of_stock_label.setText(f"المواد المنتهية: {out_of_stock_count}")
            self.total_materials_label.setText(f"إجمالي المواد: {total_materials_count}")
            self.total_products_label.setText(f"إجمالي المنتجات: {total_products_count}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحديث الإحصائيات: {str(e)}")

    # دوال إدارة المخزن
    def add_material_stock(self):
        """إضافة رصيد مادة أولية"""
        QMessageBox.information(self, "قريباً", "سيتم إضافة نافذة إضافة رصيد المواد قريباً")

    def adjust_material_stock(self):
        """تعديل رصيد مادة أولية"""
        QMessageBox.information(self, "قريباً", "سيتم إضافة نافذة تعديل رصيد المواد قريباً")

    def view_material_movements(self):
        """عرض حركات المواد"""
        QMessageBox.information(self, "قريباً", "سيتم إضافة نافذة عرض حركات المواد قريباً")

    def add_product_stock(self):
        """إضافة رصيد منتجات"""
        QMessageBox.information(self, "قريباً", "سيتم إضافة نافذة إضافة رصيد المنتجات قريباً")

    def sell_products(self):
        """تسجيل مبيعات منتجات"""
        QMessageBox.information(self, "قريباً", "سيتم إضافة نافذة تسجيل مبيعات المنتجات قريباً")

    def adjust_product_stock(self):
        """تعديل رصيد منتجات"""
        QMessageBox.information(self, "قريباً", "سيتم إضافة نافذة تعديل رصيد المنتجات قريباً")

    def add_packaging_tools(self):
        """إضافة أدوات تغليف"""
        QMessageBox.information(self, "قريباً", "سيتم إضافة نافذة إضافة أدوات التغليف قريباً")

    def packaging_out(self):
        """خروج أدوات تغليف"""
        QMessageBox.information(self, "قريباً", "سيتم إضافة نافذة خروج أدوات التغليف قريباً")

    def packaging_in(self):
        """إرجاع أدوات تغليف"""
        QMessageBox.information(self, "قريباً", "سيتم إضافة نافذة إرجاع أدوات التغليف قريباً")

    def export_inventory_report(self):
        """تصدير تقرير المخزن"""
        QMessageBox.information(self, "قريباً", "سيتم إضافة وظيفة تصدير تقرير المخزن قريباً")
