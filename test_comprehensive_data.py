#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل للبرنامج مع إدخال بيانات تجريبية
"""

import sys
import os
from datetime import datetime, date, timedelta

# إضافة مجلد src إلى المسار
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from database import db

def add_test_suppliers():
    """إضافة موردين تجريبيين"""
    print("إضافة موردين تجريبيين...")
    
    suppliers = [
        ("شركة الدقيق الذهبي", "0555123456", "الرياض - حي الملز", "مورد دقيق عالي الجودة"),
        ("مؤسسة العسل الطبيعي", "0555234567", "جدة - حي الروضة", "عسل طبيعي 100%"),
        ("شركة المكسرات المختارة", "0555345678", "الدمام - حي الفيصلية", "مكسرات مختارة ومحمصة"),
        ("مصنع الزبدة الطازجة", "0555456789", "الطائف - حي الشفا", "منتجات الألبان الطازجة"),
        ("شركة التوابل العربية", "0555567890", "المدينة المنورة", "توابل وبهارات طبيعية")
    ]
    
    for name, phone, address, notes in suppliers:
        query = "INSERT INTO suppliers (name, phone, address, notes) VALUES (?, ?, ?, ?)"
        db.execute_insert(query, (name, phone, address, notes))
    
    print(f"تم إضافة {len(suppliers)} مورد")

def add_test_materials():
    """إضافة مواد أولية تجريبية"""
    print("إضافة مواد أولية تجريبية...")
    
    materials = [
        ("دقيق أبيض", "كيلوغرام", 3.50, 500.0, 50.0, "دقيق عالي الجودة"),
        ("عسل طبيعي", "كيلوغرام", 45.00, 100.0, 10.0, "عسل سدر طبيعي"),
        ("لوز مقشر", "كيلوغرام", 35.00, 50.0, 5.0, "لوز مقشر ومحمص"),
        ("زبدة طازجة", "كيلوغرام", 25.00, 30.0, 5.0, "زبدة بقري طازجة"),
        ("سكر ناعم", "كيلوغرام", 4.00, 200.0, 20.0, "سكر أبيض ناعم"),
        ("بيض طازج", "كرتونة", 12.00, 20.0, 3.0, "بيض طازج حجم كبير"),
        ("فانيليا", "زجاجة", 8.00, 10.0, 2.0, "خلاصة فانيليا طبيعية"),
        ("ماء ورد", "زجاجة", 15.00, 15.0, 2.0, "ماء ورد طبيعي"),
        ("جوز هند مبشور", "كيلوغرام", 20.00, 25.0, 3.0, "جوز هند مبشور ناعم"),
        ("تمر منزوع النوى", "كيلوغرام", 18.00, 40.0, 5.0, "تمر مجدول منزوع النوى")
    ]
    
    for name, unit, price, stock, min_stock, notes in materials:
        query = """INSERT INTO raw_materials (name, unit, price, stock_quantity, min_stock, notes) 
                   VALUES (?, ?, ?, ?, ?, ?)"""
        db.execute_insert(query, (name, unit, price, stock, min_stock, notes))
    
    print(f"تم إضافة {len(materials)} مادة أولية")

def add_test_recipes():
    """إضافة وصفات تجريبية"""
    print("إضافة وصفات تجريبية...")
    
    recipes = [
        ("معمول التمر", 5.0, "معمول محشو بالتمر والمكسرات"),
        ("كعك العسل", 3.0, "كعك محلى بالعسل الطبيعي"),
        ("بسكويت اللوز", 2.5, "بسكويت مقرمش باللوز المحمص"),
        ("حلاوة الجبن", 4.0, "حلاوة الجبن التقليدية"),
        ("قطايف محشوة", 6.0, "قطايف محشوة بالمكسرات والعسل")
    ]
    
    recipe_ids = []
    for name, batch_weight, description in recipes:
        query = "INSERT INTO recipes (name, batch_weight, description) VALUES (?, ?, ?)"
        recipe_id = db.execute_insert(query, (name, batch_weight, description))
        recipe_ids.append(recipe_id)
    
    print(f"تم إضافة {len(recipes)} وصفة")
    return recipe_ids

def add_test_customers():
    """إضافة زبائن تجريبيين"""
    print("إضافة زبائن تجريبيين...")
    
    customers = [
        ("أحمد محمد العلي", "0501234567", "الرياض - حي النخيل", 5.0, 0, "زبون مميز"),
        ("فاطمة سعد الأحمد", "0502345678", "جدة - حي البلد", 0.0, 2, "زبونة دائمة"),
        ("محمد عبدالله الخالد", "0503456789", "الدمام - حي الشاطئ", 10.0, 1, "خصم خاص"),
        ("نورا علي السالم", "0504567890", "الطائف - حي الحوية", 0.0, 0, "زبونة جديدة"),
        ("سعد محمد الغامدي", "0505678901", "أبها - حي المنهل", 7.5, 3, "زبون تاجر"),
        ("مريم أحمد القحطاني", "0506789012", "الخبر - حي الراكة", 0.0, 1, "زبونة مناسبات"),
        ("عبدالرحمن سالم الدوسري", "0507890123", "بريدة - حي الصفراء", 15.0, 0, "خصم كبير"),
        ("هند محمد الشهري", "0508901234", "خميس مشيط - حي الموظفين", 0.0, 2, "زبونة منتظمة")
    ]
    
    customer_ids = []
    for name, phone, address, discount, trays, notes in customers:
        query = """INSERT INTO customers (name, phone, address, special_price_discount, trays_with_customer, notes) 
                   VALUES (?, ?, ?, ?, ?, ?)"""
        customer_id = db.execute_insert(query, (name, phone, address, discount, trays, notes))
        customer_ids.append(customer_id)
    
    print(f"تم إضافة {len(customers)} زبون")
    return customer_ids

def add_test_workers():
    """إضافة عمال تجريبيين"""
    print("إضافة عمال تجريبيين...")
    
    workers = [
        ("علي أحمد الصانع", "daily", 150.0, 0.0, "0551234567", "الرياض", "عامل خبز ماهر"),
        ("محمد سعد الخباز", "daily", 120.0, 0.0, "0552345678", "الرياض", "مساعد خباز"),
        ("فهد عبدالله المعجن", "monthly", 0.0, 3500.0, "0553456789", "الرياض", "رئيس العمال"),
        ("سالم محمد المساعد", "daily", 100.0, 0.0, "0554567890", "الرياض", "عامل تنظيف"),
        ("خالد علي الحارس", "monthly", 0.0, 2800.0, "0555678901", "الرياض", "حارس ليلي")
    ]
    
    worker_ids = []
    for name, worker_type, daily_rate, monthly_salary, phone, address, notes in workers:
        query = """INSERT INTO workers (name, worker_type, daily_rate, monthly_salary, phone, address, notes) 
                   VALUES (?, ?, ?, ?, ?, ?, ?)"""
        worker_id = db.execute_insert(query, (name, worker_type, daily_rate, monthly_salary, phone, address, notes))
        worker_ids.append(worker_id)
    
    print(f"تم إضافة {len(workers)} عامل")
    return worker_ids

def add_test_products(recipe_ids):
    """إضافة منتجات تجريبية"""
    print("إضافة منتجات تجريبية...")
    
    products = [
        ("معمول تمر صغير", recipe_ids[0] if recipe_ids else None, 0.5, "كيس", False, 25.0, 100),
        ("معمول تمر كبير", recipe_ids[0] if recipe_ids else None, 1.0, "علبة", True, 45.0, 50),
        ("كعك عسل قطعة", recipe_ids[1] if recipe_ids else None, 0.3, "قطعة", False, 15.0, 200),
        ("بسكويت لوز علبة", recipe_ids[2] if recipe_ids else None, 0.8, "علبة", True, 35.0, 75),
        ("حلاوة جبن صينية", recipe_ids[3] if recipe_ids else None, 2.0, "صينية", True, 80.0, 30),
        ("قطايف محشوة كيلو", recipe_ids[4] if recipe_ids else None, 1.0, "كيلو", False, 60.0, 40)
    ]
    
    product_ids = []
    for name, recipe_id, weight, packaging, returnable, price, stock in products:
        query = """INSERT INTO products (name, recipe_id, weight, packaging_type, is_returnable, price, stock_quantity) 
                   VALUES (?, ?, ?, ?, ?, ?, ?)"""
        product_id = db.execute_insert(query, (name, recipe_id, weight, packaging, returnable, price, stock))
        product_ids.append(product_id)
    
    print(f"تم إضافة {len(products)} منتج")
    return product_ids

def add_test_purchase_invoices():
    """إضافة فواتير مشتريات تجريبية"""
    print("إضافة فواتير مشتريات تجريبية...")
    
    # الحصول على معرفات الموردين
    suppliers = db.execute_query("SELECT id FROM suppliers LIMIT 3")
    if not suppliers:
        print("لا توجد موردين في قاعدة البيانات")
        return
    
    invoices = [
        (suppliers[0]['id'], "PUR-2025-001", 1500.0, 1500.0, "2025-06-15", "مؤكدة", "نقداً", "فاتورة دقيق وسكر"),
        (suppliers[1]['id'], "PUR-2025-002", 2200.0, 1000.0, "2025-06-16", "مسودة", "آجل", "فاتورة عسل ومكسرات"),
        (suppliers[2]['id'], "PUR-2025-003", 800.0, 800.0, "2025-06-17", "مدفوعة", "تحويل بنكي", "فاتورة زبدة وبيض")
    ]
    
    for supplier_id, invoice_num, total, paid, date, status, payment, notes in invoices:
        query = """INSERT INTO purchase_invoices 
                   (supplier_id, invoice_number, total_amount, paid_amount, invoice_date, status, payment_method, notes) 
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?)"""
        db.execute_insert(query, (supplier_id, invoice_num, total, paid, date, status, payment, notes))
    
    print(f"تم إضافة {len(invoices)} فاتورة مشتريات")

def add_test_sales_invoices(customer_ids, product_ids):
    """إضافة فواتير مبيعات تجريبية"""
    print("إضافة فواتير مبيعات تجريبية...")
    
    if not customer_ids or not product_ids:
        print("لا توجد زبائن أو منتجات في قاعدة البيانات")
        return
    
    invoices = [
        (customer_ids[0], "SAL-2025-001", 250.0, 250.0, 0, "2025-06-15", "فاتورة معمول وكعك"),
        (customer_ids[1], "SAL-2025-002", 180.0, 100.0, 2, "2025-06-16", "فاتورة بسكويت"),
        (customer_ids[2], "SAL-2025-003", 320.0, 200.0, 1, "2025-06-17", "فاتورة حلاوة جبن"),
        (customer_ids[3], "SAL-2025-004", 150.0, 150.0, 0, "2025-06-18", "فاتورة قطايف")
    ]
    
    for customer_id, invoice_num, total, paid, trays, date, notes in invoices:
        query = """INSERT INTO sales_invoices 
                   (customer_id, invoice_number, total_amount, paid_amount, trays_taken, invoice_date, notes) 
                   VALUES (?, ?, ?, ?, ?, ?, ?)"""
        db.execute_insert(query, (customer_id, invoice_num, total, paid, trays, date, notes))
    
    print(f"تم إضافة {len(invoices)} فاتورة مبيعات")

def main():
    """الدالة الرئيسية لإضافة البيانات التجريبية"""
    print("بدء إضافة البيانات التجريبية...")
    print("=" * 50)
    
    try:
        # إضافة البيانات الأساسية
        add_test_suppliers()
        add_test_materials()
        recipe_ids = add_test_recipes()
        customer_ids = add_test_customers()
        worker_ids = add_test_workers()
        product_ids = add_test_products(recipe_ids)
        
        # إضافة الفواتير
        add_test_purchase_invoices()
        add_test_sales_invoices(customer_ids, product_ids)
        
        print("=" * 50)
        print("✅ تم إضافة جميع البيانات التجريبية بنجاح!")
        print("يمكنك الآن اختبار جميع وظائف البرنامج")
        
    except Exception as e:
        print(f"❌ خطأ في إضافة البيانات التجريبية: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
