#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام التحجيم المتناسق لنافذة الفاتورة
"""

import sys
import os
sys.path.append('src')

def test_scaling_system():
    """اختبار نظام التحجيم المتناسق"""
    print("🧪 اختبار نظام التحجيم المتناسق...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QPushButton, QVBoxLayout, QWidget, QLabel
        from purchases_window import InvoiceDialog
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء نافذة التحكم
        control_window = QWidget()
        control_window.setWindowTitle("تحكم في التحجيم")
        control_window.setFixedSize(300, 400)
        
        layout = QVBoxLayout(control_window)
        
        # عنوان
        title = QLabel("نظام التحجيم المتناسق")
        title.setStyleSheet("font-size: 16px; font-weight: bold; padding: 10px;")
        layout.addWidget(title)
        
        # معلومات الحجم الحالي
        size_info = QLabel("الحجم الأساسي: 800×600")
        layout.addWidget(size_info)
        
        current_scale = QLabel("عامل التحجيم: 1.0")
        layout.addWidget(current_scale)
        
        # إنشاء نافذة الفاتورة
        invoice_dialog = InvoiceDialog()
        
        def update_scale(factor, description):
            """تحديث عامل التحجيم"""
            invoice_dialog.set_scale_factor(factor)
            current_scale.setText(f"عامل التحجيم: {factor}")
            size_info.setText(f"الحجم الحالي: {int(800*factor)}×{int(600*factor)}")
            print(f"✅ تم تطبيق التحجيم: {description} (عامل: {factor})")
        
        # أزرار التحجيم المختلفة
        scales = [
            (0.8, "صغير (-20%)"),
            (0.9, "صغير قليلاً (-10%)"),
            (1.0, "عادي (100%)"),
            (1.1, "كبير قليلاً (+10%)"),
            (1.2, "كبير (+20%)"),
            (1.3, "كبير جداً (+30%)")
        ]
        
        for factor, description in scales:
            btn = QPushButton(description)
            btn.clicked.connect(lambda checked, f=factor, d=description: update_scale(f, d))
            btn.setStyleSheet("padding: 8px; margin: 2px; border-radius: 4px; background: #3498DB; color: white;")
            layout.addWidget(btn)
        
        # زر إظهار/إخفاء نافذة الفاتورة
        def toggle_invoice():
            if invoice_dialog.isVisible():
                invoice_dialog.hide()
                toggle_btn.setText("إظهار نافذة الفاتورة")
            else:
                invoice_dialog.show()
                toggle_btn.setText("إخفاء نافذة الفاتورة")
        
        toggle_btn = QPushButton("إظهار نافذة الفاتورة")
        toggle_btn.clicked.connect(toggle_invoice)
        toggle_btn.setStyleSheet("padding: 10px; margin: 5px; border-radius: 5px; background: #27AE60; color: white; font-weight: bold;")
        layout.addWidget(toggle_btn)
        
        # معلومات الاستخدام
        info = QLabel("""
كيفية الاستخدام:
1. اضغط على أزرار التحجيم المختلفة
2. اضغط 'إظهار نافذة الفاتورة' لرؤية النتيجة
3. لاحظ كيف تتغير جميع العناصر بشكل متناسق

مثال: إذا قللت الحجم بـ 10%، ستقل جميع العناصر بنفس النسبة
        """)
        info.setStyleSheet("padding: 10px; background: #f0f0f0; border-radius: 5px; font-size: 10px;")
        info.setWordWrap(True)
        layout.addWidget(info)
        
        # عرض نافذة التحكم
        control_window.show()
        
        print("✅ تم إنشاء نظام التحكم في التحجيم")
        print("\n📝 جرب الآن:")
        print("   1. اضغط على أزرار التحجيم المختلفة")
        print("   2. اضغط 'إظهار نافذة الفاتورة' لرؤية التأثير")
        print("   3. لاحظ التحجيم المتناسق لجميع العناصر")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التحجيم: {str(e)}")
        import traceback
        traceback.print_exc()

def test_manual_scaling():
    """اختبار التحجيم اليدوي"""
    print("🔧 اختبار التحجيم اليدوي...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from purchases_window import InvoiceDialog
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء نافذة الفاتورة
        dialog = InvoiceDialog()
        
        print("📏 الأحجام المختلفة:")
        
        # اختبار أحجام مختلفة
        scales = [0.8, 0.9, 1.0, 1.1, 1.2]
        
        for scale in scales:
            dialog.set_scale_factor(scale)
            width = int(800 * scale)
            height = int(600 * scale)
            print(f"   عامل {scale}: {width}×{height}")
        
        # إعادة للحجم العادي
        dialog.set_scale_factor(1.0)
        dialog.show()
        
        print("✅ تم اختبار التحجيم اليدوي")
        print("📋 النافذة معروضة بالحجم العادي")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار اليدوي: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🧪 اختبار نظام التحجيم المتناسق")
    print("=" * 50)
    
    print("اختر نوع الاختبار:")
    print("1. اختبار تفاعلي مع نافذة تحكم")
    print("2. اختبار يدوي سريع")
    
    choice = input("أدخل اختيارك (1 أو 2): ").strip()
    
    if choice == "1":
        test_scaling_system()
    elif choice == "2":
        test_manual_scaling()
    else:
        print("اختيار غير صحيح. سيتم تشغيل الاختبار التفاعلي...")
        test_scaling_system()

if __name__ == "__main__":
    main()
