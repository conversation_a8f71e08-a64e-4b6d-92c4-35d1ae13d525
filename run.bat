@echo off
chcp 65001 > nul
title برنامج محاسبة مصنع الحلويات التقليدية

echo ========================================
echo 🏭 برنامج محاسبة مصنع الحلويات التقليدية
echo ========================================
echo.

echo 📦 التحقق من المتطلبات...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث
    pause
    exit /b 1
)

echo ✅ Python متوفر

echo 📋 تثبيت المتطلبات...
pip install -r requirements.txt > nul 2>&1
if errorlevel 1 (
    echo ⚠️ تحذير: قد تكون بعض المتطلبات غير مثبتة بشكل صحيح
)

echo ✅ المتطلبات جاهزة

echo.
echo 🚀 تشغيل البرنامج...
echo.

python main.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل البرنامج
    echo يرجى التحقق من:
    echo - تثبيت Python بشكل صحيح
    echo - تثبيت جميع المتطلبات
    echo - عدم وجود ملفات مفقودة
    echo.
    pause
)

echo.
echo 👋 شكراً لاستخدام البرنامج
pause
