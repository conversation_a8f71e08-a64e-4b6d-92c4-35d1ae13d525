#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لجميع وظائف البرنامج
"""

import sys
import os
sys.path.append('src')

from database import db

def test_database_functions():
    """اختبار وظائف قاعدة البيانات"""
    print("🔍 اختبار قاعدة البيانات...")
    
    try:
        # اختبار الاتصال
        suppliers = db.execute_query("SELECT COUNT(*) as count FROM suppliers")
        print(f"   ✅ عدد الموردين: {suppliers[0]['count']}")
        
        materials = db.execute_query("SELECT COUNT(*) as count FROM raw_materials")
        print(f"   ✅ عدد المواد الأولية: {materials[0]['count']}")
        
        customers = db.execute_query("SELECT COUNT(*) as count FROM customers")
        print(f"   ✅ عدد الزبائن: {customers[0]['count']}")
        
        products = db.execute_query("SELECT COUNT(*) as count FROM products")
        print(f"   ✅ عدد المنتجات: {products[0]['count']}")
        
        workers = db.execute_query("SELECT COUNT(*) as count FROM workers")
        print(f"   ✅ عدد العمال: {workers[0]['count']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في قاعدة البيانات: {str(e)}")
        return False

def test_reports_generation():
    """اختبار إنشاء التقارير"""
    print("📊 اختبار إنشاء التقارير...")
    
    try:
        from datetime import date, timedelta
        
        # تقرير المبيعات
        start_date = date.today() - timedelta(days=30)
        end_date = date.today()
        
        sales_query = """
            SELECT 
                COUNT(*) as total_invoices,
                SUM(total_amount) as total_sales,
                SUM(paid_amount) as total_paid,
                SUM(total_amount - paid_amount) as total_remaining
            FROM sales_invoices 
            WHERE invoice_date BETWEEN ? AND ?
        """
        
        sales_data = db.execute_query(sales_query, (start_date, end_date))
        
        if sales_data:
            data = sales_data[0]
            print(f"   ✅ إجمالي الفواتير: {data['total_invoices'] or 0}")
            print(f"   ✅ إجمالي المبيعات: {data['total_sales'] or 0:.2f} ريال")
            print(f"   ✅ إجمالي المدفوع: {data['total_paid'] or 0:.2f} ريال")
            print(f"   ✅ إجمالي المتبقي: {data['total_remaining'] or 0:.2f} ريال")
        
        # تقرير المديونية
        debts_query = """
            SELECT 
                c.name,
                COALESCE(SUM(si.total_amount - si.paid_amount), 0) as remaining
            FROM customers c
            LEFT JOIN sales_invoices si ON c.id = si.customer_id
            GROUP BY c.id, c.name
            HAVING remaining > 0
            ORDER BY remaining DESC
        """
        
        debts_data = db.execute_query(debts_query)
        print(f"   ✅ عدد الزبائن المدينين: {len(debts_data)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء التقارير: {str(e)}")
        return False

def test_inventory_alerts():
    """اختبار تنبيهات المخزون"""
    print("⚠️ اختبار تنبيهات المخزون...")
    
    try:
        # المواد المنخفضة
        low_stock_query = """
            SELECT name, stock_quantity, min_stock
            FROM raw_materials 
            WHERE stock_quantity <= min_stock AND stock_quantity > 0
        """
        
        low_stock = db.execute_query(low_stock_query)
        print(f"   ✅ المواد المنخفضة: {len(low_stock)}")
        
        # المواد المنتهية
        out_stock_query = """
            SELECT name, stock_quantity
            FROM raw_materials 
            WHERE stock_quantity <= 0
        """
        
        out_stock = db.execute_query(out_stock_query)
        print(f"   ✅ المواد المنتهية: {len(out_stock)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في تنبيهات المخزون: {str(e)}")
        return False

def test_payroll_calculation():
    """اختبار حساب الأجور"""
    print("💰 اختبار حساب الأجور...")
    
    try:
        from datetime import date, timedelta
        
        start_date = date.today() - timedelta(days=30)
        end_date = date.today()
        
        # جلب العمال اليوميين
        daily_workers = db.execute_query("SELECT * FROM workers WHERE worker_type = 'daily'")
        
        total_wages = 0
        for worker in daily_workers:
            # حساب العجنات
            attendance_query = """
                SELECT SUM(batches_worked) as total_batches
                FROM daily_attendance
                WHERE worker_id = ? AND work_date BETWEEN ? AND ?
            """
            attendance = db.execute_query(attendance_query, (worker['id'], start_date, end_date))
            total_batches = attendance[0]['total_batches'] or 0
            
            # حساب الأجر
            wage = total_batches * (worker['daily_rate'] or 0)
            total_wages += wage
            
            print(f"   ✅ {worker['name']}: {total_batches} عجنة = {wage:.2f} ريال")
        
        print(f"   ✅ إجمالي أجور العمال اليوميين: {total_wages:.2f} ريال")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في حساب الأجور: {str(e)}")
        return False

def test_recipe_calculations():
    """اختبار حسابات الوصفات"""
    print("🧮 اختبار حسابات الوصفات...")
    
    try:
        # جلب وصفة
        recipes = db.execute_query("SELECT * FROM recipes LIMIT 1")
        if not recipes:
            print("   ⚠️ لا توجد وصفات للاختبار")
            return True
        
        recipe = recipes[0]
        print(f"   📋 اختبار وصفة: {recipe['name']}")
        
        # جلب المكونات
        ingredients_query = """
            SELECT ri.*, rm.name as material_name, rm.unit, rm.price
            FROM recipe_ingredients ri
            LEFT JOIN raw_materials rm ON ri.material_id = rm.id
            WHERE ri.recipe_id = ?
        """
        ingredients = db.execute_query(ingredients_query, (recipe['id'],))
        
        total_cost = 0
        for ingredient in ingredients:
            cost = ingredient['quantity'] * (ingredient['price'] or 0)
            total_cost += cost
            print(f"   ✅ {ingredient['material_name']}: {ingredient['quantity']:.2f} {ingredient['unit']} = {cost:.2f} ريال")
        
        print(f"   ✅ إجمالي تكلفة العجنة: {total_cost:.2f} ريال")
        print(f"   ✅ تكلفة الكيلوغرام: {total_cost / recipe['batch_weight']:.2f} ريال")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في حسابات الوصفات: {str(e)}")
        return False

def test_backup_functionality():
    """اختبار وظائف النسخ الاحتياطي"""
    print("💾 اختبار النسخ الاحتياطي...")
    
    try:
        import os
        from datetime import datetime
        
        # إنشاء مجلد النسخ الاحتياطية
        backup_folder = "backup"
        os.makedirs(backup_folder, exist_ok=True)
        
        # إنشاء نسخة احتياطية تجريبية
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"test_backup_{timestamp}.db"
        backup_path = os.path.join(backup_folder, backup_filename)
        
        if db.backup_database(backup_path):
            print(f"   ✅ تم إنشاء نسخة احتياطية: {backup_filename}")
            
            # التحقق من حجم الملف
            if os.path.exists(backup_path):
                file_size = os.path.getsize(backup_path) / 1024  # بالكيلوبايت
                print(f"   ✅ حجم النسخة الاحتياطية: {file_size:.2f} كيلوبايت")
                
                # حذف النسخة التجريبية
                os.remove(backup_path)
                print(f"   ✅ تم حذف النسخة التجريبية")
            
            return True
        else:
            print("   ❌ فشل في إنشاء النسخة الاحتياطية")
            return False
        
    except Exception as e:
        print(f"   ❌ خطأ في النسخ الاحتياطي: {str(e)}")
        return False

def test_data_integrity():
    """اختبار سلامة البيانات"""
    print("🔍 اختبار سلامة البيانات...")
    
    try:
        issues = []
        
        # فحص الجداول الأساسية
        tables = ['suppliers', 'raw_materials', 'customers', 'products', 'recipes', 'workers']
        for table in tables:
            try:
                count = db.execute_query(f"SELECT COUNT(*) as count FROM {table}")[0]['count']
                print(f"   ✅ جدول {table}: {count} سجل")
            except Exception as e:
                issues.append(f"مشكلة في جدول {table}: {str(e)}")
        
        # فحص العلاقات
        try:
            # فحص المنتجات المرتبطة بوصفات
            orphan_products = db.execute_query("""
                SELECT COUNT(*) as count FROM products 
                WHERE recipe_id IS NOT NULL AND recipe_id NOT IN (SELECT id FROM recipes)
            """)[0]['count']
            
            if orphan_products > 0:
                issues.append(f"منتجات غير مرتبطة بوصفات: {orphan_products}")
            else:
                print(f"   ✅ جميع المنتجات مرتبطة بوصفات صحيحة")
            
        except Exception as e:
            issues.append(f"خطأ في فحص العلاقات: {str(e)}")
        
        if not issues:
            print("   ✅ جميع البيانات سليمة")
            return True
        else:
            for issue in issues:
                print(f"   ⚠️ {issue}")
            return False
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص سلامة البيانات: {str(e)}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار شامل لبرنامج محاسبة مصنع الحلويات التقليدية")
    print("=" * 60)
    
    tests = [
        ("قاعدة البيانات", test_database_functions),
        ("التقارير", test_reports_generation),
        ("تنبيهات المخزون", test_inventory_alerts),
        ("حساب الأجور", test_payroll_calculation),
        ("حسابات الوصفات", test_recipe_calculations),
        ("النسخ الاحتياطي", test_backup_functionality),
        ("سلامة البيانات", test_data_integrity),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_function in tests:
        print(f"\n🔄 اختبار {test_name}...")
        try:
            if test_function():
                print(f"✅ نجح اختبار {test_name}")
                passed_tests += 1
            else:
                print(f"❌ فشل اختبار {test_name}")
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {passed_tests}/{total_tests} اختبار نجح")
    
    if passed_tests == total_tests:
        print("🎉 جميع الاختبارات نجحت! البرنامج جاهز للاستخدام.")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
