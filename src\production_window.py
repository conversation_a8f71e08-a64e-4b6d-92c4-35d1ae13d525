#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة الإنتاج
إدارة الوصفات والمنتجات وتسجيل الإنتاج اليومي
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QGridLayout, QPushButton, QLabel, QFrame, 
                             QTableWidget, QTableWidgetItem, QLineEdit, 
                             QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                             QMessageBox, QDialog, QFormLayout, QDialogButtonBox,
                             QTabWidget, QHeaderView, QDateEdit, QGroupBox,
                             QListWidget, QListWidgetItem, QSplitter)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont, QIcon
from database import db

class ProductionWindow(QMainWindow):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إدارة الإنتاج")

        # تحديد أبعاد مناسبة للشاشات المتوسطة
        self.setFixedSize(1200, 650)

        # توسيط النافذة
        self.center_window()
        
        # إعداد التصميم
        self.setup_style()
        
        # إنشاء الواجهة
        self.setup_ui()
        
        # تحديث البيانات
        self.refresh_data()

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        from PyQt5.QtWidgets import QApplication
        screen = QApplication.desktop().screenGeometry()
        window = self.frameGeometry()

        # حساب الموقع المركزي
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2

        # التأكد من أن النافذة لا تخرج عن حدود الشاشة
        x = max(0, min(x, screen.width() - window.width()))
        y = max(0, min(y, screen.height() - window.height()))

        self.move(x, y)
    
    def setup_style(self):
        """إعداد تصميم النافذة"""
        self.colors = {
            'primary': '#2C3E50',
            'secondary': '#3498DB', 
            'accent': '#E74C3C',
            'success': '#27AE60',
            'warning': '#F39C12',
            'light': '#ECF0F1',
            'dark': '#34495E',
            'white': '#FFFFFF'
        }
        
        self.setStyleSheet(f"""
            QMainWindow {{
                background: {self.colors['light']};
            }}
            
            QTabWidget::pane {{
                border: 2px solid {self.colors['primary']};
                border-radius: 10px;
                background: {self.colors['white']};
            }}
            
            QTabBar::tab {{
                background: {self.colors['secondary']};
                color: white;
                padding: 10px 20px;
                margin: 2px;
                border-radius: 5px;
                font-weight: bold;
                font-family: 'Tahoma';
            }}
            
            QTabBar::tab:selected {{
                background: {self.colors['primary']};
            }}
            
            QPushButton {{
                background: {self.colors['secondary']};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
                font-family: 'Tahoma';
                min-height: 35px;
            }}
            
            QPushButton:hover {{
                background: {self.colors['accent']};
            }}
            
            QPushButton:pressed {{
                background: {self.colors['dark']};
            }}
            
            QTableWidget {{
                background: {self.colors['white']};
                border: 2px solid {self.colors['light']};
                border-radius: 8px;
                gridline-color: {self.colors['light']};
                font-family: 'Tahoma';
            }}
            
            QTableWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {self.colors['light']};
            }}
            
            QTableWidget::item:selected {{
                background: {self.colors['secondary']};
                color: white;
            }}
            
            QHeaderView::section {{
                background: {self.colors['primary']};
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
                font-family: 'Tahoma';
            }}
            
            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {{
                border: 2px solid {self.colors['light']};
                border-radius: 5px;
                padding: 8px;
                font-family: 'Tahoma';
                background: {self.colors['white']};
            }}
            
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {{
                border-color: {self.colors['secondary']};
            }}
            
            QGroupBox {{
                font-weight: bold;
                font-family: 'Tahoma';
                border: 2px solid {self.colors['light']};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                color: {self.colors['primary']};
            }}
            
            QListWidget {{
                background: {self.colors['white']};
                border: 2px solid {self.colors['light']};
                border-radius: 8px;
                font-family: 'Tahoma';
            }}
            
            QListWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {self.colors['light']};
            }}
            
            QListWidget::item:selected {{
                background: {self.colors['secondary']};
                color: white;
            }}
        """)
    
    def setup_ui(self):
        """إنشاء واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # إنشاء الهيدر
        header = self.create_header()
        main_layout.addWidget(header)
        
        # إنشاء التبويبات
        tabs = QTabWidget()
        
        # تبويب الوصفات
        recipes_tab = self.create_recipes_tab()
        tabs.addTab(recipes_tab, "الوصفات")
        
        # تبويب المنتجات
        products_tab = self.create_products_tab()
        tabs.addTab(products_tab, "المنتجات")
        
        # تبويب الإنتاج اليومي
        daily_production_tab = self.create_daily_production_tab()
        tabs.addTab(daily_production_tab, "الإنتاج اليومي")
        
        main_layout.addWidget(tabs)
        
        # إنشاء أزرار التحكم
        control_buttons = self.create_control_buttons()
        main_layout.addWidget(control_buttons)
    
    def create_header(self):
        """إنشاء منطقة الهيدر"""
        header_frame = QFrame()
        header_frame.setFixedHeight(80)
        header_frame.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {self.colors['primary']}, stop:1 {self.colors['secondary']});
                border-radius: 15px;
            }}
        """)
        
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(20, 10, 20, 10)
        
        # أيقونة ونص الهيدر
        icon_label = QLabel("🏭")
        icon_label.setFont(QFont("Arial", 32))
        icon_label.setStyleSheet("color: white; background: transparent;")
        
        title_label = QLabel("إدارة الإنتاج")
        title_label.setFont(QFont("Tahoma", 20, QFont.Bold))
        title_label.setStyleSheet("color: white; background: transparent;")
        
        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        return header_frame
    
    def create_recipes_tab(self):
        """إنشاء تبويب الوصفات"""
        tab_widget = QWidget()
        layout = QHBoxLayout(tab_widget)
        
        # الجانب الأيسر - قائمة الوصفات
        left_side = QVBoxLayout()
        
        # أزرار التحكم في الوصفات
        recipes_buttons = QHBoxLayout()
        
        add_recipe_btn = QPushButton("إضافة وصفة جديدة")
        add_recipe_btn.clicked.connect(self.add_recipe)
        
        edit_recipe_btn = QPushButton("تعديل وصفة")
        edit_recipe_btn.clicked.connect(self.edit_recipe)
        
        delete_recipe_btn = QPushButton("حذف وصفة")
        delete_recipe_btn.clicked.connect(self.delete_recipe)
        delete_recipe_btn.setStyleSheet(f"background: {self.colors['accent']};")
        
        recipes_buttons.addWidget(add_recipe_btn)
        recipes_buttons.addWidget(edit_recipe_btn)
        recipes_buttons.addWidget(delete_recipe_btn)
        
        left_side.addLayout(recipes_buttons)
        
        # قائمة الوصفات
        self.recipes_list = QListWidget()
        self.recipes_list.itemClicked.connect(self.on_recipe_selected)
        left_side.addWidget(self.recipes_list)
        
        # الجانب الأيمن - تفاصيل الوصفة
        right_side = QVBoxLayout()
        
        # معلومات الوصفة
        recipe_info_group = QGroupBox("معلومات الوصفة")
        recipe_info_layout = QFormLayout(recipe_info_group)
        
        self.recipe_name_label = QLabel("اختر وصفة لعرض التفاصيل")
        self.recipe_weight_label = QLabel("")
        self.recipe_description_label = QLabel("")
        
        recipe_info_layout.addRow("اسم الوصفة:", self.recipe_name_label)
        recipe_info_layout.addRow("وزن العجنة:", self.recipe_weight_label)
        recipe_info_layout.addRow("الوصف:", self.recipe_description_label)
        
        right_side.addWidget(recipe_info_group)
        
        # مكونات الوصفة
        ingredients_group = QGroupBox("مكونات الوصفة")
        ingredients_layout = QVBoxLayout(ingredients_group)
        
        # أزرار إدارة المكونات
        ingredients_buttons = QHBoxLayout()
        
        add_ingredient_btn = QPushButton("إضافة مكون")
        add_ingredient_btn.clicked.connect(self.add_ingredient)
        
        edit_ingredient_btn = QPushButton("تعديل مكون")
        edit_ingredient_btn.clicked.connect(self.edit_ingredient)
        
        remove_ingredient_btn = QPushButton("حذف مكون")
        remove_ingredient_btn.clicked.connect(self.remove_ingredient)
        remove_ingredient_btn.setStyleSheet(f"background: {self.colors['accent']};")
        
        ingredients_buttons.addWidget(add_ingredient_btn)
        ingredients_buttons.addWidget(edit_ingredient_btn)
        ingredients_buttons.addWidget(remove_ingredient_btn)
        
        ingredients_layout.addLayout(ingredients_buttons)
        
        # جدول المكونات
        self.ingredients_table = QTableWidget()
        self.ingredients_table.setColumnCount(4)
        self.ingredients_table.setHorizontalHeaderLabels([
            "المادة", "الكمية", "الوحدة", "التكلفة"
        ])
        
        # تعديل عرض الأعمدة
        header = self.ingredients_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        
        ingredients_layout.addWidget(self.ingredients_table)
        
        right_side.addWidget(ingredients_group)
        
        # إضافة الجانبين للتخطيط الرئيسي
        splitter = QSplitter(Qt.Horizontal)
        
        left_widget = QWidget()
        left_widget.setLayout(left_side)
        
        right_widget = QWidget()
        right_widget.setLayout(right_side)
        
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)
        splitter.setSizes([400, 600])
        
        layout.addWidget(splitter)
        
        return tab_widget

    def create_products_tab(self):
        """إنشاء تبويب المنتجات"""
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        add_product_btn = QPushButton("إضافة منتج جديد")
        add_product_btn.clicked.connect(self.add_product)

        edit_product_btn = QPushButton("تعديل منتج")
        edit_product_btn.clicked.connect(self.edit_product)

        delete_product_btn = QPushButton("حذف منتج")
        delete_product_btn.clicked.connect(self.delete_product)
        delete_product_btn.setStyleSheet(f"background: {self.colors['accent']};")

        buttons_layout.addWidget(add_product_btn)
        buttons_layout.addWidget(edit_product_btn)
        buttons_layout.addWidget(delete_product_btn)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        # جدول المنتجات
        self.products_table = QTableWidget()
        self.products_table.setColumnCount(7)
        self.products_table.setHorizontalHeaderLabels([
            "الرقم", "اسم المنتج", "الوصفة", "الوزن", "نوع التغليف", "السعر", "الكمية المتوفرة"
        ])

        # تعديل عرض الأعمدة
        header = self.products_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)

        layout.addWidget(self.products_table)

        return tab_widget

    def create_daily_production_tab(self):
        """إنشاء تبويب الإنتاج اليومي"""
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        add_production_btn = QPushButton("تسجيل إنتاج جديد")
        add_production_btn.clicked.connect(self.add_daily_production)

        view_production_btn = QPushButton("عرض تفاصيل الإنتاج")
        view_production_btn.clicked.connect(self.view_production_details)

        calculate_materials_btn = QPushButton("حساب المواد المطلوبة")
        calculate_materials_btn.clicked.connect(self.calculate_required_materials)
        calculate_materials_btn.setStyleSheet(f"background: {self.colors['success']};")

        buttons_layout.addWidget(add_production_btn)
        buttons_layout.addWidget(view_production_btn)
        buttons_layout.addWidget(calculate_materials_btn)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        # جدول الإنتاج اليومي
        self.daily_production_table = QTableWidget()
        self.daily_production_table.setColumnCount(7)
        self.daily_production_table.setHorizontalHeaderLabels([
            "الرقم", "التاريخ", "الوصفة", "عدد العجنات", "الوزن الإجمالي", "العسل الموزع", "ملاحظات"
        ])

        # تعديل عرض الأعمدة
        header = self.daily_production_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.Stretch)

        layout.addWidget(self.daily_production_table)

        return tab_widget

    def create_control_buttons(self):
        """إنشاء أزرار التحكم السفلية"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)

        refresh_btn = QPushButton("تحديث البيانات")
        refresh_btn.clicked.connect(self.refresh_data)
        refresh_btn.setStyleSheet(f"background: {self.colors['success']};")

        back_btn = QPushButton("العودة للقائمة الرئيسية")
        back_btn.clicked.connect(self.close)
        back_btn.setStyleSheet(f"background: {self.colors['dark']};")

        buttons_layout.addStretch()
        buttons_layout.addWidget(refresh_btn)
        buttons_layout.addWidget(back_btn)

        return buttons_frame

    def refresh_data(self):
        """تحديث جميع البيانات"""
        self.load_recipes()
        self.load_products()
        self.load_daily_production()
        self.clear_recipe_details()

    def load_recipes(self):
        """تحميل بيانات الوصفات"""
        try:
            recipes = db.execute_query("SELECT * FROM recipes ORDER BY name")

            self.recipes_list.clear()

            for recipe in recipes:
                item = QListWidgetItem(f"{recipe['name']} ({recipe['batch_weight']} كغ)")
                item.setData(Qt.UserRole, recipe['id'])
                self.recipes_list.addItem(item)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات الوصفات: {str(e)}")

    def load_products(self):
        """تحميل بيانات المنتجات"""
        try:
            query = """
                SELECT p.*, r.name as recipe_name
                FROM products p
                LEFT JOIN recipes r ON p.recipe_id = r.id
                ORDER BY p.name
            """
            products = db.execute_query(query)

            self.products_table.setRowCount(len(products))

            for row, product in enumerate(products):
                self.products_table.setItem(row, 0, QTableWidgetItem(str(product['id'])))
                self.products_table.setItem(row, 1, QTableWidgetItem(product['name'] or ''))
                self.products_table.setItem(row, 2, QTableWidgetItem(product['recipe_name'] or ''))
                self.products_table.setItem(row, 3, QTableWidgetItem(f"{product['weight']:.2f} كغ"))
                self.products_table.setItem(row, 4, QTableWidgetItem(product['packaging_type'] or ''))
                self.products_table.setItem(row, 5, QTableWidgetItem(f"{product['price']:.2f}"))
                self.products_table.setItem(row, 6, QTableWidgetItem(f"{product['stock_quantity']:.2f}"))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات المنتجات: {str(e)}")

    def load_daily_production(self):
        """تحميل بيانات الإنتاج اليومي"""
        try:
            query = """
                SELECT dp.*, r.name as recipe_name
                FROM daily_production dp
                LEFT JOIN recipes r ON dp.recipe_id = r.id
                ORDER BY dp.production_date DESC
            """
            productions = db.execute_query(query)

            self.daily_production_table.setRowCount(len(productions))

            for row, production in enumerate(productions):
                self.daily_production_table.setItem(row, 0, QTableWidgetItem(str(production['id'])))
                self.daily_production_table.setItem(row, 1, QTableWidgetItem(production['production_date'] or ''))
                self.daily_production_table.setItem(row, 2, QTableWidgetItem(production['recipe_name'] or ''))
                self.daily_production_table.setItem(row, 3, QTableWidgetItem(str(production['batches_count'])))
                self.daily_production_table.setItem(row, 4, QTableWidgetItem(f"{production['total_weight']:.2f} كغ"))
                self.daily_production_table.setItem(row, 5, QTableWidgetItem(f"{production['honey_distributed']:.2f} كغ"))
                self.daily_production_table.setItem(row, 6, QTableWidgetItem(production['notes'] or ''))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات الإنتاج اليومي: {str(e)}")

    def on_recipe_selected(self, item):
        """عند اختيار وصفة من القائمة"""
        recipe_id = item.data(Qt.UserRole)
        self.load_recipe_details(recipe_id)

    def load_recipe_details(self, recipe_id):
        """تحميل تفاصيل الوصفة"""
        try:
            # جلب معلومات الوصفة
            recipe = db.execute_query("SELECT * FROM recipes WHERE id = ?", (recipe_id,))
            if not recipe:
                return

            recipe = recipe[0]

            # عرض معلومات الوصفة
            self.recipe_name_label.setText(recipe['name'])
            self.recipe_weight_label.setText(f"{recipe['batch_weight']} كيلوغرام")
            self.recipe_description_label.setText(recipe['description'] or 'لا توجد ملاحظات')

            # جلب مكونات الوصفة
            query = """
                SELECT ri.*, rm.name as material_name, rm.unit, rm.price
                FROM recipe_ingredients ri
                LEFT JOIN raw_materials rm ON ri.material_id = rm.id
                WHERE ri.recipe_id = ?
                ORDER BY rm.name
            """
            ingredients = db.execute_query(query, (recipe_id,))

            # عرض المكونات في الجدول
            self.ingredients_table.setRowCount(len(ingredients))

            for row, ingredient in enumerate(ingredients):
                self.ingredients_table.setItem(row, 0, QTableWidgetItem(ingredient['material_name'] or ''))
                self.ingredients_table.setItem(row, 1, QTableWidgetItem(f"{ingredient['quantity']:.2f}"))
                self.ingredients_table.setItem(row, 2, QTableWidgetItem(ingredient['unit'] or ''))

                # حساب التكلفة
                cost = ingredient['quantity'] * (ingredient['price'] or 0)
                self.ingredients_table.setItem(row, 3, QTableWidgetItem(f"{cost:.2f}"))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل تفاصيل الوصفة: {str(e)}")

    def clear_recipe_details(self):
        """مسح تفاصيل الوصفة"""
        self.recipe_name_label.setText("اختر وصفة لعرض التفاصيل")
        self.recipe_weight_label.setText("")
        self.recipe_description_label.setText("")
        self.ingredients_table.setRowCount(0)

    # دوال إدارة الوصفات
    def add_recipe(self):
        """إضافة وصفة جديدة"""
        QMessageBox.information(self, "قريباً", "سيتم إضافة نافذة إضافة الوصفات قريباً")

    def edit_recipe(self):
        """تعديل وصفة"""
        current_item = self.recipes_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار وصفة للتعديل")
            return

        QMessageBox.information(self, "قريباً", "سيتم إضافة نافذة تعديل الوصفات قريباً")

    def delete_recipe(self):
        """حذف وصفة"""
        current_item = self.recipes_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار وصفة للحذف")
            return

        recipe_id = current_item.data(Qt.UserRole)
        recipe_name = current_item.text().split(' (')[0]

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف الوصفة '{recipe_name}'؟\nسيتم حذف جميع المكونات والمنتجات المرتبطة بها.",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # حذف مكونات الوصفة
                db.execute_update("DELETE FROM recipe_ingredients WHERE recipe_id = ?", (recipe_id,))

                # تحديث المنتجات المرتبطة
                db.execute_update("UPDATE products SET recipe_id = NULL WHERE recipe_id = ?", (recipe_id,))

                # حذف الوصفة
                db.execute_update("DELETE FROM recipes WHERE id = ?", (recipe_id,))

                QMessageBox.information(self, "نجح", "تم حذف الوصفة بنجاح")
                self.load_recipes()
                self.clear_recipe_details()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في حذف الوصفة: {str(e)}")

    def add_ingredient(self):
        """إضافة مكون للوصفة"""
        current_item = self.recipes_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار وصفة أولاً")
            return

        QMessageBox.information(self, "قريباً", "سيتم إضافة نافذة إضافة المكونات قريباً")

    def edit_ingredient(self):
        """تعديل مكون في الوصفة"""
        current_row = self.ingredients_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مكون للتعديل")
            return

        QMessageBox.information(self, "قريباً", "سيتم إضافة نافذة تعديل المكونات قريباً")

    def remove_ingredient(self):
        """حذف مكون من الوصفة"""
        current_row = self.ingredients_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مكون للحذف")
            return

        QMessageBox.information(self, "قريباً", "سيتم إضافة وظيفة حذف المكونات قريباً")

    # دوال إدارة المنتجات
    def add_product(self):
        """إضافة منتج جديد"""
        QMessageBox.information(self, "قريباً", "سيتم إضافة نافذة إضافة المنتجات قريباً")

    def edit_product(self):
        """تعديل منتج"""
        current_row = self.products_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج للتعديل")
            return

        QMessageBox.information(self, "قريباً", "سيتم إضافة نافذة تعديل المنتجات قريباً")

    def delete_product(self):
        """حذف منتج"""
        current_row = self.products_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج للحذف")
            return

        product_id = int(self.products_table.item(current_row, 0).text())
        product_name = self.products_table.item(current_row, 1).text()

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف المنتج '{product_name}'؟",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                db.execute_update("DELETE FROM products WHERE id = ?", (product_id,))
                QMessageBox.information(self, "نجح", "تم حذف المنتج بنجاح")
                self.load_products()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في حذف المنتج: {str(e)}")

    # دوال الإنتاج اليومي
    def add_daily_production(self):
        """تسجيل إنتاج يومي جديد"""
        QMessageBox.information(self, "قريباً", "سيتم إضافة نافذة تسجيل الإنتاج اليومي قريباً")

    def view_production_details(self):
        """عرض تفاصيل الإنتاج"""
        current_row = self.daily_production_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار سجل إنتاج لعرض التفاصيل")
            return

        QMessageBox.information(self, "قريباً", "سيتم إضافة نافذة عرض تفاصيل الإنتاج قريباً")

    def calculate_required_materials(self):
        """حساب المواد المطلوبة للإنتاج"""
        QMessageBox.information(self, "قريباً", "سيتم إضافة نافذة حساب المواد المطلوبة قريباً")
