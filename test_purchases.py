#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وحدة المشتريات
"""

import sys
import os
sys.path.append('src')

from PyQt5.QtWidgets import QApplication
from purchases_window import PurchasesWindow

def test_purchases_window():
    """اختبار نافذة المشتريات"""
    
    print("🔄 اختبار نافذة المشتريات...")
    
    try:
        app = QApplication(sys.argv)
        
        # إنشاء نافذة المشتريات
        window = PurchasesWindow()
        
        print("✅ تم إنشاء نافذة المشتريات بنجاح")
        
        # عرض النافذة
        window.show()
        
        print("✅ تم عرض النافذة بنجاح")
        print("📝 يمكنك الآن اختبار إضافة الموردين والمواد")
        print("🔧 إذا واجهت مشكلة، أغلق النافذة وأخبرني بالتفاصيل")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نافذة المشتريات: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_purchases_window()
