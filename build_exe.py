#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف بناء البرنامج التنفيذي
إنشاء ملف exe باستخدام PyInstaller
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def create_exe():
    """إنشاء ملف exe للبرنامج"""
    
    print("🚀 بدء عملية إنشاء ملف التشغيل...")
    
    # التأكد من وجود PyInstaller
    try:
        import PyInstaller
        print("✅ PyInstaller متوفر")
    except ImportError:
        print("❌ PyInstaller غير متوفر. يرجى تثبيته أولاً:")
        print("pip install pyinstaller")
        return False
    
    # إنشاء مجلد البناء
    build_dir = "build_output"
    if os.path.exists(build_dir):
        shutil.rmtree(build_dir)
    os.makedirs(build_dir, exist_ok=True)
    
    # إنشاء مجلد الموارد
    assets_dir = "assets"
    os.makedirs(assets_dir, exist_ok=True)
    
    # إنشاء أيقونة البرنامج (ملف نصي مؤقت)
    icon_content = """
    هذا ملف مؤقت لأيقونة البرنامج
    يمكن استبداله بملف .ico حقيقي
    """
    with open(os.path.join(assets_dir, "icon_info.txt"), 'w', encoding='utf-8') as f:
        f.write(icon_content)
    
    # معاملات PyInstaller
    pyinstaller_args = [
        'pyinstaller',
        '--onefile',                    # ملف واحد
        '--windowed',                   # بدون نافذة الكونسول
        '--name=برنامج_محاسبة_الحلويات',  # اسم الملف
        '--distpath=' + build_dir,      # مجلد الإخراج
        '--workpath=temp_build',        # مجلد العمل المؤقت
        '--specpath=temp_build',        # مجلد ملف المواصفات
        '--add-data=src;src',           # إضافة مجلد الكود المصدري
        '--add-data=database;database', # إضافة مجلد قاعدة البيانات
        '--add-data=assets;assets',     # إضافة مجلد الموارد
        '--hidden-import=PyQt5.QtCore',
        '--hidden-import=PyQt5.QtGui',
        '--hidden-import=PyQt5.QtWidgets',
        '--hidden-import=sqlite3',
        '--hidden-import=reportlab',
        'main.py'                       # الملف الرئيسي
    ]
    
    print("📦 بدء عملية البناء...")
    
    try:
        # تشغيل PyInstaller
        result = subprocess.run(pyinstaller_args, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ تم إنشاء ملف التشغيل بنجاح!")
            
            # نسخ الملفات الإضافية
            exe_path = os.path.join(build_dir, "برنامج_محاسبة_الحلويات.exe")
            if os.path.exists(exe_path):
                print(f"📁 مسار الملف: {os.path.abspath(exe_path)}")
                
                # إنشاء ملف معلومات
                info_file = os.path.join(build_dir, "معلومات_البرنامج.txt")
                with open(info_file, 'w', encoding='utf-8') as f:
                    f.write(f"""
برنامج محاسبة مصنع الحلويات التقليدية
========================================

تاريخ البناء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
الإصدار: 1.0
المطور: Augment Agent

متطلبات التشغيل:
- نظام التشغيل: Windows 7 أو أحدث
- ذاكرة الوصول العشوائي: 2 جيجابايت على الأقل
- مساحة القرص الصلب: 100 ميجابايت

طريقة التشغيل:
1. انقر نقراً مزدوجاً على ملف "برنامج_محاسبة_الحلويات.exe"
2. انتظر حتى يتم تحميل البرنامج
3. ابدأ باستخدام البرنامج

ملاحظات:
- يتم إنشاء قاعدة البيانات تلقائياً عند التشغيل الأول
- يُنصح بإنشاء نسخة احتياطية دورية من البيانات
- للدعم الفني، راجع دليل المستخدم

تم التطوير بواسطة: Augment Agent
التاريخ: 2025-06-18
                    """)
                
                # نسخ ملف المتطلبات
                shutil.copy2("requirements.txt", build_dir)
                shutil.copy2("README.md", build_dir)
                
                print("📋 تم نسخ الملفات الإضافية")
                
            else:
                print("❌ لم يتم العثور على ملف التشغيل")
                return False
                
        else:
            print("❌ فشل في إنشاء ملف التشغيل:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ خطأ في عملية البناء: {str(e)}")
        return False
    
    finally:
        # تنظيف الملفات المؤقتة
        if os.path.exists("temp_build"):
            shutil.rmtree("temp_build")
        
        # حذف ملفات PyInstaller المؤقتة
        for file in ["برنامج_محاسبة_الحلويات.spec"]:
            if os.path.exists(file):
                os.remove(file)
    
    print("🎉 تمت عملية البناء بنجاح!")
    print(f"📂 يمكنك العثور على الملفات في مجلد: {os.path.abspath(build_dir)}")
    
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 تثبيت المتطلبات...")
    
    try:
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم تثبيت المتطلبات بنجاح")
            return True
        else:
            print("❌ فشل في تثبيت المتطلبات:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تثبيت المتطلبات: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🏭 برنامج محاسبة مصنع الحلويات التقليدية")
    print("🔧 أداة بناء ملف التشغيل")
    print("=" * 50)
    
    # تثبيت المتطلبات أولاً
    if not install_requirements():
        print("❌ فشل في تثبيت المتطلبات. يرجى المحاولة مرة أخرى.")
        return
    
    # إنشاء ملف التشغيل
    if create_exe():
        print("\n🎊 تم إنشاء البرنامج بنجاح!")
        print("يمكنك الآن توزيع البرنامج على أجهزة أخرى.")
    else:
        print("\n❌ فشل في إنشاء البرنامج.")

if __name__ == "__main__":
    main()
