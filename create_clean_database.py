#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء قاعدة بيانات نظيفة بدون بيانات تجريبية
"""

import sys
import os
sys.path.append('src')

from database import db

def create_clean_database():
    """إنشاء قاعدة بيانات نظيفة"""
    
    print("🔄 إنشاء قاعدة بيانات نظيفة...")
    
    try:
        # التأكد من وجود مجلد قاعدة البيانات
        os.makedirs("database", exist_ok=True)
        
        # إنشاء الجداول الفارغة
        db.create_tables()
        
        print("✅ تم إنشاء قاعدة بيانات نظيفة بنجاح!")
        
        # التحقق من الجداول
        tables = [
            'suppliers', 'raw_materials', 'customers', 'products', 
            'recipes', 'recipe_ingredients', 'workers', 'daily_attendance',
            'worker_advances', 'purchase_invoices', 'purchase_invoice_items',
            'sales_invoices', 'sales_invoice_items', 'daily_production',
            'packaging_tools'
        ]
        
        print("\n📊 الجداول المنشأة:")
        for table in tables:
            try:
                count = db.execute_query(f"SELECT COUNT(*) as count FROM {table}")[0]['count']
                print(f"   ✅ {table}: {count} سجل")
            except Exception as e:
                print(f"   ❌ {table}: خطأ - {str(e)}")
        
        print("\n🎉 قاعدة البيانات جاهزة للاستخدام!")
        print("📝 يمكنك الآن تشغيل البرنامج وإدخال بياناتك الحقيقية")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("=" * 50)
    print("🏭 برنامج محاسبة مصنع الحلويات التقليدية")
    print("🗃️ إنشاء قاعدة بيانات نظيفة")
    print("=" * 50)
    
    if create_clean_database():
        print("\n✅ تمت العملية بنجاح!")
    else:
        print("\n❌ فشلت العملية!")

if __name__ == "__main__":
    main()
