#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة المشتريات
إدارة الموردين والمواد الأولية وفواتير المشتريات
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QGridLayout, QPushButton, QLabel, QFrame,
                             QTableWidget, QTableWidgetItem, QLineEdit,
                             QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                             QMessageBox, QDialog, QFormLayout, QDialogButtonBox,
                             QTabWidget, QHeaderView, QDateEdit, QGroupBox,
                             QAbstractItemView, QApplication)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont, QIcon, QColor
from database import db

class PurchasesWindow(QMainWindow):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إدارة المشتريات")

        # تحديد أبعاد مناسبة ومريحة
        self.setFixedSize(1000, 600)

        # توسيط النافذة
        self.center_window()
        
        # إعداد التصميم
        self.setup_style()
        
        # إنشاء الواجهة
        self.setup_ui()
        
        # تحديث البيانات
        self.refresh_data()

    def center_window(self):
        """توسيط النافذة في الشاشة بدقة"""
        from PyQt5.QtWidgets import QApplication
        # معالجة الأحداث أولاً لضمان تحديث الأبعاد
        QApplication.processEvents()

        screen = QApplication.desktop().screenGeometry()

        # الحصول على أبعاد النافذة الحالية
        window_width = self.width()
        window_height = self.height()

        # حساب الموقع المركزي
        x = (screen.width() - window_width) // 2
        y = (screen.height() - window_height) // 2

        # التأكد من أن النافذة لا تخرج عن حدود الشاشة
        x = max(0, min(x, screen.width() - window_width))
        y = max(0, min(y, screen.height() - window_height))

        self.move(x, y)
    
    def setup_style(self):
        """إعداد تصميم النافذة"""
        self.colors = {
            'primary': '#2C3E50',
            'secondary': '#3498DB',
            'accent': '#E74C3C',
            'success': '#27AE60',
            'warning': '#F39C12',
            'info': '#17A2B8',
            'light': '#ECF0F1',
            'dark': '#34495E',
            'white': '#FFFFFF'
        }
        
        self.setStyleSheet(f"""
            QMainWindow {{
                background: {self.colors['light']};
            }}
            
            QTabWidget::pane {{
                border: 2px solid {self.colors['primary']};
                border-radius: 10px;
                background: {self.colors['white']};
            }}
            
            QTabBar::tab {{
                background: {self.colors['secondary']};
                color: white;
                padding: 12px 25px;
                margin: 2px;
                border-radius: 5px;
                font-weight: bold;
                font-family: 'Tahoma';
                font-size: 13px;
                min-width: 120px;
            }}
            
            QTabBar::tab:selected {{
                background: {self.colors['primary']};
            }}
            
            QPushButton {{
                background: {self.colors['secondary']};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 25px;
                font-weight: bold;
                font-family: 'Tahoma';
                font-size: 12px;
                min-height: 40px;
                min-width: 140px;
            }}
            
            QPushButton:hover {{
                background: {self.colors['accent']};
            }}
            
            QPushButton:pressed {{
                background: {self.colors['dark']};
            }}
            
            QTableWidget {{
                background: {self.colors['white']};
                border: 2px solid {self.colors['light']};
                border-radius: 8px;
                gridline-color: {self.colors['light']};
                font-family: 'Tahoma';
            }}
            
            QTableWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {self.colors['light']};
            }}
            
            QTableWidget::item:selected {{
                background: {self.colors['secondary']};
                color: white;
            }}
            
            QHeaderView::section {{
                background: {self.colors['primary']};
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
                font-family: 'Tahoma';
            }}
            
            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {{
                border: 2px solid {self.colors['light']};
                border-radius: 5px;
                padding: 8px;
                font-family: 'Tahoma';
                background: {self.colors['white']};
            }}
            
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {{
                border-color: {self.colors['secondary']};
            }}
            
            QGroupBox {{
                font-weight: bold;
                font-family: 'Tahoma';
                border: 2px solid {self.colors['light']};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                color: {self.colors['primary']};
            }}
        """)
    
    def setup_ui(self):
        """إنشاء واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # إنشاء الهيدر
        header = self.create_header()
        main_layout.addWidget(header)
        
        # إنشاء التبويبات
        tabs = QTabWidget()
        
        # تبويب الموردين
        suppliers_tab = self.create_suppliers_tab()
        tabs.addTab(suppliers_tab, "الموردين")
        
        # تبويب المواد الأولية
        materials_tab = self.create_materials_tab()
        tabs.addTab(materials_tab, "المواد الأولية")
        
        # تبويب فواتير المشتريات
        invoices_tab = self.create_invoices_tab()
        tabs.addTab(invoices_tab, "فواتير المشتريات")
        
        main_layout.addWidget(tabs)
        
        # إنشاء أزرار التحكم
        control_buttons = self.create_control_buttons()
        main_layout.addWidget(control_buttons)
    
    def create_header(self):
        """إنشاء منطقة الهيدر"""
        header_frame = QFrame()
        header_frame.setFixedHeight(80)
        header_frame.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {self.colors['primary']}, stop:1 {self.colors['secondary']});
                border-radius: 15px;
            }}
        """)
        
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(20, 10, 20, 10)
        
        # أيقونة ونص الهيدر
        icon_label = QLabel("🛒")
        icon_label.setFont(QFont("Arial", 32))
        icon_label.setStyleSheet("color: white; background: transparent;")
        
        title_label = QLabel("إدارة المشتريات")
        title_label.setFont(QFont("Tahoma", 20, QFont.Bold))
        title_label.setStyleSheet("color: white; background: transparent;")
        
        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        return header_frame
    
    def create_suppliers_tab(self):
        """إنشاء تبويب الموردين"""
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        add_supplier_btn = QPushButton("إضافة مورد جديد")
        add_supplier_btn.clicked.connect(self.add_supplier)
        
        edit_supplier_btn = QPushButton("تعديل مورد")
        edit_supplier_btn.clicked.connect(self.edit_supplier)
        
        delete_supplier_btn = QPushButton("حذف مورد")
        delete_supplier_btn.clicked.connect(self.delete_supplier)
        delete_supplier_btn.setStyleSheet(f"background: {self.colors['accent']};")
        
        buttons_layout.addWidget(add_supplier_btn)
        buttons_layout.addWidget(edit_supplier_btn)
        buttons_layout.addWidget(delete_supplier_btn)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        
        # جدول الموردين
        self.suppliers_table = QTableWidget()
        self.suppliers_table.setColumnCount(5)
        self.suppliers_table.setHorizontalHeaderLabels([
            "الرقم", "اسم المورد", "رقم الهاتف", "العنوان", "ملاحظات"
        ])
        
        # تعديل عرض الأعمدة
        header = self.suppliers_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.Stretch)
        header.setSectionResizeMode(4, QHeaderView.Stretch)
        
        layout.addWidget(self.suppliers_table)
        
        return tab_widget

    def create_materials_tab(self):
        """إنشاء تبويب المواد الأولية"""
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        add_material_btn = QPushButton("إضافة مادة جديدة")
        add_material_btn.clicked.connect(self.add_material)

        edit_material_btn = QPushButton("تعديل مادة")
        edit_material_btn.clicked.connect(self.edit_material)

        delete_material_btn = QPushButton("حذف مادة")
        delete_material_btn.clicked.connect(self.delete_material)
        delete_material_btn.setStyleSheet(f"background: {self.colors['accent']};")

        buttons_layout.addWidget(add_material_btn)
        buttons_layout.addWidget(edit_material_btn)
        buttons_layout.addWidget(delete_material_btn)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        # جدول المواد الأولية
        self.materials_table = QTableWidget()
        self.materials_table.setColumnCount(7)
        self.materials_table.setHorizontalHeaderLabels([
            "الرقم", "اسم المادة", "وحدة القياس", "السعر", "الكمية المتوفرة", "الحد الأدنى", "ملاحظات"
        ])

        # تعديل عرض الأعمدة
        header = self.materials_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.Stretch)

        layout.addWidget(self.materials_table)

        return tab_widget

    def create_invoices_tab(self):
        """إنشاء تبويب فواتير المشتريات"""
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)

        # أزرار التحكم المحسنة
        buttons_layout = QHBoxLayout()

        add_invoice_btn = QPushButton("إضافة فاتورة جديدة")
        add_invoice_btn.clicked.connect(self.add_invoice)
        add_invoice_btn.setStyleSheet(f"background: {self.colors['primary']}; color: white; font-weight: bold; padding: 8px 15px; border-radius: 5px;")

        edit_invoice_btn = QPushButton("تعديل الفاتورة")
        edit_invoice_btn.clicked.connect(self.edit_invoice)
        edit_invoice_btn.setStyleSheet(f"background: {self.colors['secondary']}; color: white; font-weight: bold; padding: 8px 15px; border-radius: 5px;")

        delete_invoice_btn = QPushButton("حذف الفاتورة")
        delete_invoice_btn.clicked.connect(self.delete_invoice)
        delete_invoice_btn.setStyleSheet(f"background: {self.colors['accent']}; color: white; font-weight: bold; padding: 8px 15px; border-radius: 5px;")

        view_invoice_btn = QPushButton("عرض الفاتورة")
        view_invoice_btn.clicked.connect(self.view_invoice)
        view_invoice_btn.setStyleSheet(f"background: {self.colors['info']}; color: white; font-weight: bold; padding: 8px 15px; border-radius: 5px;")

        print_invoice_btn = QPushButton("طباعة الفاتورة")
        print_invoice_btn.clicked.connect(self.print_invoice)
        print_invoice_btn.setStyleSheet(f"background: {self.colors['success']}; color: white; font-weight: bold; padding: 8px 15px; border-radius: 5px;")

        buttons_layout.addWidget(add_invoice_btn)
        buttons_layout.addWidget(edit_invoice_btn)
        buttons_layout.addWidget(delete_invoice_btn)
        buttons_layout.addWidget(view_invoice_btn)
        buttons_layout.addWidget(print_invoice_btn)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        # جدول فواتير المشتريات المحسن
        self.invoices_table = QTableWidget()
        self.invoices_table.setColumnCount(9)
        self.invoices_table.setHorizontalHeaderLabels([
            "الرقم", "المورد", "رقم الفاتورة", "المبلغ الإجمالي", "المبلغ المدفوع", "المبلغ المتبقي", "تاريخ الفاتورة", "الحالة", "طريقة الدفع"
        ])

        # تعديل عرض الأعمدة
        header = self.invoices_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(8, QHeaderView.ResizeToContents)

        layout.addWidget(self.invoices_table)

        return tab_widget

    def create_control_buttons(self):
        """إنشاء أزرار التحكم السفلية"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)

        refresh_btn = QPushButton("تحديث البيانات")
        refresh_btn.clicked.connect(self.refresh_data)
        refresh_btn.setStyleSheet(f"background: {self.colors['success']};")

        back_btn = QPushButton("العودة للقائمة الرئيسية")
        back_btn.clicked.connect(self.close)
        back_btn.setStyleSheet(f"background: {self.colors['dark']};")

        buttons_layout.addStretch()
        buttons_layout.addWidget(refresh_btn)
        buttons_layout.addWidget(back_btn)

        return buttons_frame

    def refresh_data(self):
        """تحديث جميع البيانات"""
        self.load_suppliers()
        self.load_materials()
        self.load_invoices()

    def load_suppliers(self):
        """تحميل بيانات الموردين"""
        try:
            suppliers = db.execute_query("SELECT * FROM suppliers ORDER BY name")

            self.suppliers_table.setRowCount(len(suppliers))

            for row, supplier in enumerate(suppliers):
                self.suppliers_table.setItem(row, 0, QTableWidgetItem(str(supplier['id'])))
                self.suppliers_table.setItem(row, 1, QTableWidgetItem(supplier['name'] or ''))
                self.suppliers_table.setItem(row, 2, QTableWidgetItem(supplier['phone'] or ''))
                self.suppliers_table.setItem(row, 3, QTableWidgetItem(supplier['address'] or ''))
                self.suppliers_table.setItem(row, 4, QTableWidgetItem(supplier['notes'] or ''))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات الموردين: {str(e)}")

    def load_materials(self):
        """تحميل بيانات المواد الأولية"""
        try:
            materials = db.execute_query("SELECT * FROM raw_materials ORDER BY name")

            self.materials_table.setRowCount(len(materials))

            for row, material in enumerate(materials):
                self.materials_table.setItem(row, 0, QTableWidgetItem(str(material['id'])))
                self.materials_table.setItem(row, 1, QTableWidgetItem(material['name'] or ''))
                self.materials_table.setItem(row, 2, QTableWidgetItem(material['unit'] or ''))
                self.materials_table.setItem(row, 3, QTableWidgetItem(f"{material['price']:.2f}"))
                self.materials_table.setItem(row, 4, QTableWidgetItem(f"{material['stock_quantity']:.2f}"))
                self.materials_table.setItem(row, 5, QTableWidgetItem(f"{material['min_stock']:.2f}"))
                self.materials_table.setItem(row, 6, QTableWidgetItem(material['notes'] or ''))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات المواد الأولية: {str(e)}")

    def load_invoices(self):
        """تحميل بيانات فواتير المشتريات المحسنة"""
        try:
            # التحقق من وجود الحقول الجديدة في قاعدة البيانات
            columns_info = db.execute_query("PRAGMA table_info(purchase_invoices)")
            existing_columns = [col['name'] for col in columns_info]

            # بناء الاستعلام حسب الحقول المتوفرة
            if 'status' in existing_columns and 'payment_method' in existing_columns:
                query = """
                    SELECT pi.*, s.name as supplier_name
                    FROM purchase_invoices pi
                    LEFT JOIN suppliers s ON pi.supplier_id = s.id
                    ORDER BY pi.invoice_date DESC
                """
            else:
                # إذا لم تكن الحقول الجديدة موجودة، استخدم الحقول الأساسية فقط
                query = """
                    SELECT pi.id, pi.supplier_id, pi.invoice_number, pi.total_amount,
                           pi.paid_amount, pi.invoice_date, pi.notes, pi.created_at,
                           s.name as supplier_name
                    FROM purchase_invoices pi
                    LEFT JOIN suppliers s ON pi.supplier_id = s.id
                    ORDER BY pi.invoice_date DESC
                """

            invoices = db.execute_query(query)
            self.invoices_table.setRowCount(len(invoices))

            for row, invoice in enumerate(invoices):
                try:
                    # الرقم
                    id_item = QTableWidgetItem(str(invoice['id']))
                    id_item.setTextAlignment(Qt.AlignCenter)
                    self.invoices_table.setItem(row, 0, id_item)

                    # المورد
                    supplier_item = QTableWidgetItem(invoice['supplier_name'] or 'غير محدد')
                    self.invoices_table.setItem(row, 1, supplier_item)

                    # رقم الفاتورة
                    invoice_num_item = QTableWidgetItem(invoice['invoice_number'] or '')
                    invoice_num_item.setTextAlignment(Qt.AlignCenter)
                    self.invoices_table.setItem(row, 2, invoice_num_item)

                    # المبلغ الإجمالي
                    total_amount = float(invoice.get('total_amount', 0))
                    total_item = QTableWidgetItem(f"{total_amount:.2f} دج")
                    total_item.setTextAlignment(Qt.AlignCenter)
                    total_item.setBackground(QColor("#E8F5E8"))  # خلفية خضراء فاتحة
                    self.invoices_table.setItem(row, 3, total_item)

                    # المبلغ المدفوع
                    paid_amount = float(invoice.get('paid_amount', 0))
                    paid_item = QTableWidgetItem(f"{paid_amount:.2f} دج")
                    paid_item.setTextAlignment(Qt.AlignCenter)
                    paid_item.setBackground(QColor("#E3F2FD"))  # خلفية زرقاء فاتحة
                    self.invoices_table.setItem(row, 4, paid_item)

                    # المبلغ المتبقي
                    remaining = total_amount - paid_amount
                    remaining_item = QTableWidgetItem(f"{remaining:.2f} دج")
                    remaining_item.setTextAlignment(Qt.AlignCenter)
                    if remaining > 0:
                        remaining_item.setBackground(QColor("#FFEBEE"))  # خلفية حمراء فاتحة
                        remaining_item.setForeground(QColor("#D32F2F"))  # نص أحمر
                    else:
                        remaining_item.setBackground(QColor("#E8F5E8"))  # خلفية خضراء فاتحة
                        remaining_item.setForeground(QColor("#388E3C"))  # نص أخضر
                    self.invoices_table.setItem(row, 5, remaining_item)

                    # تاريخ الفاتورة
                    date_item = QTableWidgetItem(invoice.get('invoice_date', '') or '')
                    date_item.setTextAlignment(Qt.AlignCenter)
                    self.invoices_table.setItem(row, 6, date_item)

                    # الحالة (مع التحقق من وجود الحقل)
                    if 'status' in existing_columns:
                        status = invoice.get('status', 'مسودة') or 'مسودة'
                    else:
                        status = 'مسودة'  # قيمة افتراضية

                    status_item = QTableWidgetItem(status)
                    status_item.setTextAlignment(Qt.AlignCenter)
                    # تلوين حسب الحالة
                    if status == 'مؤكدة':
                        status_item.setBackground(QColor("#E8F5E8"))
                    elif status == 'مدفوعة':
                        status_item.setBackground(QColor("#E3F2FD"))
                    elif status == 'ملغاة':
                        status_item.setBackground(QColor("#FFEBEE"))
                    else:  # مسودة
                        status_item.setBackground(QColor("#FFF3E0"))
                    self.invoices_table.setItem(row, 7, status_item)

                    # طريقة الدفع (مع التحقق من وجود الحقل)
                    if 'payment_method' in existing_columns:
                        payment_method = invoice.get('payment_method', 'نقداً') or 'نقداً'
                    else:
                        payment_method = 'نقداً'  # قيمة افتراضية

                    payment_item = QTableWidgetItem(payment_method)
                    payment_item.setTextAlignment(Qt.AlignCenter)
                    self.invoices_table.setItem(row, 8, payment_item)

                except Exception as row_error:
                    print(f"خطأ في معالجة الصف {row}: {row_error}")
                    # إضافة صف فارغ في حالة الخطأ
                    for col in range(9):
                        self.invoices_table.setItem(row, col, QTableWidgetItem("خطأ"))

        except Exception as e:
            print(f"تفاصيل الخطأ: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات فواتير المشتريات: {str(e)}\n\nتأكد من تحديث قاعدة البيانات باستخدام update_database.py")

    # دوال إدارة الموردين
    def add_supplier(self):
        """إضافة مورد جديد"""
        try:
            dialog = SupplierDialog(self)
            if dialog.exec_() == QDialog.Accepted:
                data = dialog.get_data()
                try:
                    query = """
                        INSERT INTO suppliers (name, phone, address, notes)
                        VALUES (?, ?, ?, ?)
                    """
                    db.execute_insert(query, (data['name'], data['phone'], data['address'], data['notes']))
                    QMessageBox.information(self, "نجح", "تم إضافة المورد بنجاح")
                    self.load_suppliers()
                except Exception as e:
                    QMessageBox.critical(self, "خطأ", f"خطأ في إضافة المورد: {str(e)}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة إضافة المورد: {str(e)}")

    def edit_supplier(self):
        """تعديل مورد"""
        current_row = self.suppliers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد للتعديل")
            return

        supplier_id = int(self.suppliers_table.item(current_row, 0).text())

        # جلب بيانات المورد الحالية
        supplier = db.execute_query("SELECT * FROM suppliers WHERE id = ?", (supplier_id,))
        if not supplier:
            QMessageBox.warning(self, "خطأ", "لم يتم العثور على المورد")
            return

        supplier = supplier[0]

        dialog = SupplierDialog(self, supplier)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            try:
                query = """
                    UPDATE suppliers
                    SET name = ?, phone = ?, address = ?, notes = ?
                    WHERE id = ?
                """
                db.execute_update(query, (data['name'], data['phone'], data['address'], data['notes'], supplier_id))
                QMessageBox.information(self, "نجح", "تم تعديل المورد بنجاح")
                self.load_suppliers()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في تعديل المورد: {str(e)}")

    def delete_supplier(self):
        """حذف مورد"""
        current_row = self.suppliers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد للحذف")
            return

        supplier_id = int(self.suppliers_table.item(current_row, 0).text())
        supplier_name = self.suppliers_table.item(current_row, 1).text()

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف المورد '{supplier_name}'؟\nسيتم حذف جميع فواتير المشتريات المرتبطة به.",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # حذف تفاصيل الفواتير أولاً
                db.execute_update("""
                    DELETE FROM purchase_invoice_items
                    WHERE invoice_id IN (SELECT id FROM purchase_invoices WHERE supplier_id = ?)
                """, (supplier_id,))

                # حذف الفواتير
                db.execute_update("DELETE FROM purchase_invoices WHERE supplier_id = ?", (supplier_id,))

                # حذف المورد
                db.execute_update("DELETE FROM suppliers WHERE id = ?", (supplier_id,))

                QMessageBox.information(self, "نجح", "تم حذف المورد بنجاح")
                self.load_suppliers()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في حذف المورد: {str(e)}")

    # دوال إدارة المواد الأولية
    def add_material(self):
        """إضافة مادة أولية جديدة مع فحص التكرار"""
        try:
            dialog = MaterialDialog(self)
            if dialog.exec_() == QDialog.Accepted:
                data = dialog.get_data()
                try:
                    # فحص وجود مادة بنفس الاسم
                    existing_material = db.execute_query(
                        "SELECT * FROM raw_materials WHERE LOWER(name) = LOWER(?)",
                        (data['name'],)
                    )

                    if existing_material:
                        # إذا كانت المادة موجودة، منع الإضافة
                        QMessageBox.warning(
                            self, "مادة موجودة",
                            f"المادة '{data['name']}' موجودة بالفعل في النظام.\n\n"
                            f"لا يمكن إضافة مادة مكررة.\n"
                            f"يمكنك تعديل المادة الموجودة أو استخدام اسم مختلف."
                        )
                        return

                    # إضافة مادة جديدة (تعريفية فقط)
                    query = """
                        INSERT INTO raw_materials (name, unit, price, stock_quantity, min_stock, notes)
                        VALUES (?, ?, 0, 0, ?, ?)
                    """
                    db.execute_insert(query, (
                        data['name'], data['unit'], data['min_stock'], data['notes']
                    ))
                    QMessageBox.information(self, "نجح", "تم إضافة المادة الجديدة بنجاح")
                    self.load_materials()

                except Exception as e:
                    QMessageBox.critical(self, "خطأ", f"خطأ في إضافة المادة: {str(e)}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة إضافة المادة: {str(e)}")

    def edit_material(self):
        """تعديل مادة أولية"""
        current_row = self.materials_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مادة للتعديل")
            return

        material_id = int(self.materials_table.item(current_row, 0).text())

        # جلب بيانات المادة الحالية
        material = db.execute_query("SELECT * FROM raw_materials WHERE id = ?", (material_id,))
        if not material:
            QMessageBox.warning(self, "خطأ", "لم يتم العثور على المادة")
            return

        material = material[0]

        dialog = MaterialDialog(self, material)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            try:
                query = """
                    UPDATE raw_materials
                    SET name = ?, unit = ?, min_stock = ?, notes = ?
                    WHERE id = ?
                """
                db.execute_update(query, (
                    data['name'], data['unit'], data['min_stock'], data['notes'], material_id
                ))
                QMessageBox.information(self, "نجح", "تم تعديل المادة بنجاح")
                self.load_materials()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في تعديل المادة: {str(e)}")

    def delete_material(self):
        """حذف مادة أولية"""
        current_row = self.materials_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مادة للحذف")
            return

        material_id = int(self.materials_table.item(current_row, 0).text())
        material_name = self.materials_table.item(current_row, 1).text()

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف المادة '{material_name}'؟",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                db.execute_update("DELETE FROM raw_materials WHERE id = ?", (material_id,))
                QMessageBox.information(self, "نجح", "تم حذف المادة بنجاح")
                self.load_materials()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في حذف المادة: {str(e)}")

    # دوال إدارة الفواتير
    def add_invoice(self):
        """إضافة فاتورة مشتريات جديدة"""
        try:
            dialog = InvoiceDialog(self)
            if dialog.exec_() == QDialog.Accepted:
                data = dialog.get_data()
                try:
                    # إضافة الفاتورة الرئيسية
                    invoice_query = """
                        INSERT INTO purchase_invoices (supplier_id, invoice_number, total_amount, paid_amount, invoice_date, notes, status, payment_method)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """
                    invoice_id = db.execute_insert(invoice_query, (
                        data['supplier_id'], data['invoice_number'], data['total_amount'],
                        data['paid_amount'], data['invoice_date'], data['notes'],
                        data.get('status', 'مسودة'), data.get('payment_method', 'نقداً')
                    ))

                    # إضافة تفاصيل المواد
                    for item in data['items']:
                        item_query = """
                            INSERT INTO purchase_invoice_items (invoice_id, material_id, quantity, unit_price, total_price)
                            VALUES (?, ?, ?, ?, ?)
                        """
                        db.execute_insert(item_query, (
                            invoice_id, item['material_id'], item['quantity'],
                            item['unit_price'], item['total_price']
                        ))

                        # تحديث رصيد المادة والسعر في المخزون مع حساب متوسط السعر المرجح
                        # جلب البيانات الحالية للمادة
                        current_material = db.execute_query(
                            "SELECT stock_quantity, price FROM raw_materials WHERE id = ?",
                            (item['material_id'],)
                        )

                        if current_material:
                            old_quantity = current_material[0]['stock_quantity']
                            old_price = current_material[0]['price']
                            new_quantity = item['quantity']
                            new_price = item['unit_price']

                            # حساب الكمية الإجمالية الجديدة
                            total_quantity = old_quantity + new_quantity

                            # حساب متوسط السعر المرجح
                            if total_quantity > 0:
                                weighted_avg_price = ((old_quantity * old_price) + (new_quantity * new_price)) / total_quantity
                            else:
                                weighted_avg_price = new_price

                            # تحديث الكمية والسعر
                            update_stock_query = """
                                UPDATE raw_materials
                                SET stock_quantity = ?, price = ?
                                WHERE id = ?
                            """
                            db.execute_query(update_stock_query, (total_quantity, weighted_avg_price, item['material_id']))
                        else:
                            # إذا لم توجد المادة، أضفها فقط للكمية
                            update_stock_query = """
                                UPDATE raw_materials
                                SET stock_quantity = stock_quantity + ?
                                WHERE id = ?
                            """
                            db.execute_query(update_stock_query, (item['quantity'], item['material_id']))

                    QMessageBox.information(self, "نجح", f"تم إضافة الفاتورة بنجاح\nرقم الفاتورة: {data['invoice_number']}\nتم تحديث رصيد {len(data['items'])} مادة في المخزون")
                    self.load_invoices()

                except Exception as e:
                    QMessageBox.critical(self, "خطأ", f"خطأ في إضافة الفاتورة: {str(e)}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة إضافة الفاتورة: {str(e)}")

    def edit_invoice(self):
        """تعديل فاتورة مشتريات موجودة"""
        current_row = self.invoices_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة للتعديل")
            return

        invoice_id = int(self.invoices_table.item(current_row, 0).text())

        try:
            dialog = InvoiceDialog(self, invoice_id)
            if dialog.exec_() == QDialog.Accepted:
                data = dialog.get_data()
                try:
                    # تحديث الفاتورة الرئيسية
                    update_query = """
                        UPDATE purchase_invoices
                        SET supplier_id=?, invoice_number=?, total_amount=?, paid_amount=?,
                            invoice_date=?, notes=?, status=?, payment_method=?
                        WHERE id=?
                    """
                    db.execute_query(update_query, (
                        data['supplier_id'], data['invoice_number'], data['total_amount'],
                        data['paid_amount'], data['invoice_date'], data['notes'],
                        data.get('status', 'مسودة'), data.get('payment_method', 'نقداً'),
                        invoice_id
                    ))

                    # حذف تفاصيل المواد القديمة
                    db.execute_query("DELETE FROM purchase_invoice_items WHERE invoice_id = ?", (invoice_id,))

                    # إضافة تفاصيل المواد الجديدة
                    for item in data['items']:
                        item_query = """
                            INSERT INTO purchase_invoice_items (invoice_id, material_id, quantity, unit_price, total_price)
                            VALUES (?, ?, ?, ?, ?)
                        """
                        db.execute_insert(item_query, (
                            invoice_id, item['material_id'], item['quantity'],
                            item['unit_price'], item['total_price']
                        ))

                    QMessageBox.information(self, "نجح", f"تم تعديل الفاتورة بنجاح\nرقم الفاتورة: {data['invoice_number']}")
                    self.load_invoices()

                except Exception as e:
                    QMessageBox.critical(self, "خطأ", f"خطأ في تعديل الفاتورة: {str(e)}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة تعديل الفاتورة: {str(e)}")

    def delete_invoice(self):
        """حذف فاتورة مشتريات"""
        current_row = self.invoices_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة للحذف")
            return

        invoice_id = int(self.invoices_table.item(current_row, 0).text())
        invoice_number = self.invoices_table.item(current_row, 1).text()

        # تأكيد الحذف
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف الفاتورة رقم '{invoice_number}'؟\n\nتحذير: سيتم حذف جميع تفاصيل الفاتورة نهائياً!",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # حذف تفاصيل المواد أولاً
                db.execute_query("DELETE FROM purchase_invoice_items WHERE invoice_id = ?", (invoice_id,))

                # حذف الفاتورة الرئيسية
                db.execute_query("DELETE FROM purchase_invoices WHERE id = ?", (invoice_id,))

                QMessageBox.information(self, "نجح", f"تم حذف الفاتورة رقم '{invoice_number}' بنجاح")
                self.load_invoices()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في حذف الفاتورة: {str(e)}")

    def view_invoice(self):
        """عرض تفاصيل الفاتورة"""
        current_row = self.invoices_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة للعرض")
            return

        invoice_id = int(self.invoices_table.item(current_row, 0).text())

        try:
            dialog = InvoiceViewDialog(self, invoice_id)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في عرض الفاتورة: {str(e)}")

    def print_invoice(self):
        """طباعة الفاتورة"""
        current_row = self.invoices_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة للطباعة")
            return

        invoice_id = int(self.invoices_table.item(current_row, 0).text())

        try:
            self.generate_invoice_report(invoice_id)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في طباعة الفاتورة: {str(e)}")

    def generate_invoice_report(self, invoice_id):
        """إنشاء تقرير الفاتورة"""
        try:
            # جلب بيانات الفاتورة
            query = """
                SELECT pi.*, s.name as supplier_name, s.phone as supplier_phone, s.address as supplier_address
                FROM purchase_invoices pi
                LEFT JOIN suppliers s ON pi.supplier_id = s.id
                WHERE pi.id = ?
            """
            invoice_data = db.execute_query(query, (invoice_id,))

            if not invoice_data:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على الفاتورة")
                return

            invoice = invoice_data[0]

            # إنشاء التقرير
            from datetime import datetime
            import os

            # إنشاء مجلد التقارير
            os.makedirs("reports", exist_ok=True)

            # اسم الملف
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"reports/فاتورة_مشتريات_{invoice['invoice_number']}_{timestamp}.txt"

            # محتوى التقرير
            report_content = f"""
===============================================
            فاتورة مشتريات
===============================================

رقم الفاتورة: {invoice['invoice_number']}
تاريخ الفاتورة: {invoice['invoice_date']}
تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

-----------------------------------------------
                بيانات المورد
-----------------------------------------------
اسم المورد: {invoice['supplier_name'] or 'غير محدد'}
رقم الهاتف: {invoice['supplier_phone'] or 'غير محدد'}
العنوان: {invoice['supplier_address'] or 'غير محدد'}

-----------------------------------------------
                تفاصيل الفاتورة
-----------------------------------------------
المبلغ الإجمالي: {invoice['total_amount']:.2f} دج
المبلغ المدفوع: {invoice['paid_amount']:.2f} دج
المبلغ المتبقي: {(invoice['total_amount'] - invoice['paid_amount']):.2f} دج

الملاحظات: {invoice['notes'] or 'لا توجد ملاحظات'}

===============================================
        برنامج محاسبة مصنع الحلويات التقليدية
===============================================
"""

            # كتابة التقرير
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report_content)

            QMessageBox.information(self, "نجح", f"تم إنشاء تقرير الفاتورة:\n{filename}")

            # فتح الملف
            os.startfile(filename)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء تقرير الفاتورة: {str(e)}")


class SupplierDialog(QDialog):
    """نافذة حوار إضافة/تعديل مورد"""

    def __init__(self, parent=None, supplier_data=None):
        super().__init__(parent)
        self.supplier_data = supplier_data
        self.setWindowTitle("إضافة مورد جديد" if not supplier_data else "تعديل مورد")
        self.setFixedSize(500, 400)

        # توسيط النافذة
        self.center_dialog()

        self.setup_ui()

        if supplier_data:
            self.load_data()

    def center_dialog(self):
        """توسيط نافذة الحوار"""
        if self.parent():
            parent_geometry = self.parent().geometry()
            x = parent_geometry.x() + (parent_geometry.width() - self.width()) // 2
            y = parent_geometry.y() + (parent_geometry.height() - self.height()) // 2
            self.move(x, y)

    def setup_ui(self):
        """إنشاء واجهة النافذة"""
        layout = QVBoxLayout(self)

        # إنشاء النموذج
        form_layout = QFormLayout()

        # حقل اسم المورد
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("أدخل اسم المورد")
        form_layout.addRow("اسم المورد:", self.name_edit)

        # حقل رقم الهاتف
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("أدخل رقم الهاتف")
        form_layout.addRow("رقم الهاتف:", self.phone_edit)

        # حقل العنوان
        self.address_edit = QTextEdit()
        self.address_edit.setPlaceholderText("أدخل العنوان")
        self.address_edit.setMaximumHeight(80)
        form_layout.addRow("العنوان:", self.address_edit)

        # حقل الملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("أدخل أي ملاحظات إضافية")
        self.notes_edit.setMaximumHeight(100)
        form_layout.addRow("ملاحظات:", self.notes_edit)

        layout.addLayout(form_layout)

        # أزرار التحكم
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)

        # تخصيص النصوص
        buttons.button(QDialogButtonBox.Ok).setText("حفظ")
        buttons.button(QDialogButtonBox.Cancel).setText("إلغاء")

        layout.addWidget(buttons)

    def load_data(self):
        """تحميل البيانات للتعديل"""
        if self.supplier_data:
            self.name_edit.setText(self.supplier_data['name'] or '')
            self.phone_edit.setText(self.supplier_data['phone'] or '')
            self.address_edit.setPlainText(self.supplier_data['address'] or '')
            self.notes_edit.setPlainText(self.supplier_data['notes'] or '')

    def get_data(self):
        """الحصول على البيانات المدخلة"""
        return {
            'name': self.name_edit.text().strip(),
            'phone': self.phone_edit.text().strip(),
            'address': self.address_edit.toPlainText().strip(),
            'notes': self.notes_edit.toPlainText().strip()
        }

    def accept(self):
        """التحقق من صحة البيانات قبل الحفظ"""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المورد")
            self.name_edit.setFocus()
            return

        super().accept()


class MaterialDialog(QDialog):
    """نافذة حوار إضافة/تعديل مادة أولية"""

    def __init__(self, parent=None, material_data=None):
        super().__init__(parent)
        self.material_data = material_data
        self.setWindowTitle("إضافة مادة جديدة" if not material_data else "تعديل مادة")
        self.setFixedSize(500, 500)

        # توسيط النافذة
        self.center_dialog()

        self.setup_ui()

        if material_data:
            self.load_data()

    def center_dialog(self):
        """توسيط نافذة الحوار"""
        if self.parent():
            parent_geometry = self.parent().geometry()
            x = parent_geometry.x() + (parent_geometry.width() - self.width()) // 2
            y = parent_geometry.y() + (parent_geometry.height() - self.height()) // 2
            self.move(x, y)

    def setup_ui(self):
        """إنشاء واجهة النافذة"""
        layout = QVBoxLayout(self)

        # إنشاء النموذج
        form_layout = QFormLayout()

        # حقل اسم المادة
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("أدخل اسم المادة")
        form_layout.addRow("اسم المادة:", self.name_edit)

        # حقل وحدة القياس
        self.unit_combo = QComboBox()
        self.unit_combo.addItems(["كيلوغرام", "غرام", "لتر", "صندوق", "كيس", "علبة", "قطعة", "متر"])
        self.unit_combo.setEditable(True)
        form_layout.addRow("وحدة القياس:", self.unit_combo)

        # ملاحظة توضيحية
        note_label = QLabel("ملاحظة: السعر والكمية سيتم إدخالهما عند عملية الشراء")
        note_label.setStyleSheet("color: #666; font-style: italic; padding: 5px;")
        form_layout.addRow("", note_label)

        # حقل الحد الأدنى
        self.min_stock_spin = QDoubleSpinBox()
        self.min_stock_spin.setRange(0, 999999.99)
        self.min_stock_spin.setDecimals(2)
        form_layout.addRow("الحد الأدنى:", self.min_stock_spin)

        # حقل الملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("أدخل أي ملاحظات إضافية")
        self.notes_edit.setMaximumHeight(100)
        form_layout.addRow("ملاحظات:", self.notes_edit)

        layout.addLayout(form_layout)

        # أزرار التحكم
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)

        # تخصيص النصوص
        buttons.button(QDialogButtonBox.Ok).setText("حفظ")
        buttons.button(QDialogButtonBox.Cancel).setText("إلغاء")

        layout.addWidget(buttons)

    def load_data(self):
        """تحميل البيانات للتعديل"""
        if self.material_data:
            self.name_edit.setText(self.material_data['name'] or '')
            self.unit_combo.setCurrentText(self.material_data['unit'] or '')
            self.min_stock_spin.setValue(self.material_data['min_stock'] or 0)
            self.notes_edit.setPlainText(self.material_data['notes'] or '')

    def get_data(self):
        """الحصول على البيانات المدخلة"""
        return {
            'name': self.name_edit.text().strip(),
            'unit': self.unit_combo.currentText().strip(),
            'min_stock': self.min_stock_spin.value(),
            'notes': self.notes_edit.toPlainText().strip()
        }

    def accept(self):
        """التحقق من صحة البيانات قبل الحفظ"""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المادة")
            self.name_edit.setFocus()
            return

        if not self.unit_combo.currentText().strip():
            QMessageBox.warning(self, "خطأ", "يرجى اختيار وحدة القياس")
            self.unit_combo.setFocus()
            return

        super().accept()


class InvoiceDialog(QDialog):
    """نافذة حوار إضافة فاتورة مشتريات محسنة ومتناسقة"""

    def __init__(self, parent=None, invoice_id=None):
        super().__init__(parent)
        self.invoice_id = invoice_id
        self.setWindowTitle("إدارة فواتير المشتريات")

        # حجم النافذة مثل نافذة إدارة المشتريات: العرض = 1000px — الارتفاع = 600px
        self.setFixedSize(1000, 600)

        # اتجاه النصوص من اليمين لليسار
        self.setLayoutDirection(Qt.RightToLeft)

        # قائمة المواد المضافة للفاتورة
        self.invoice_items = []

        # إعداد نظام الألوان والتصميم
        self.setup_style()

        # توسيط النافذة في الشاشة تلقائياً
        self.center_dialog()

        self.setup_ui()
        self.load_suppliers()
        self.load_materials()  # إضافة تحميل المواد
        if not self.invoice_id:
            self.generate_invoice_number()
        else:
            self.load_invoice_data()

    def setup_style(self):
        """إعداد تصميم النافذة مثل نافذة إدارة المشتريات"""
        self.colors = {
            'primary': '#2C3E50',
            'secondary': '#3498DB',
            'accent': '#E74C3C',
            'success': '#27AE60',
            'warning': '#F39C12',
            'info': '#17A2B8',
            'light': '#ECF0F1',
            'dark': '#34495E',
            'white': '#FFFFFF'
        }

        self.setStyleSheet(f"""
            QDialog {{
                background: {self.colors['light']};
            }}

            QPushButton {{
                background: {self.colors['secondary']};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 15px;
                font-weight: bold;
                font-family: 'Tahoma';
                font-size: 11px;
                min-height: 25px;
            }}

            QPushButton:hover {{
                background: {self.colors['accent']};
            }}

            QPushButton:pressed {{
                background: {self.colors['dark']};
            }}

            QTableWidget {{
                background: {self.colors['white']};
                border: 2px solid {self.colors['light']};
                border-radius: 8px;
                gridline-color: {self.colors['light']};
                font-family: 'Tahoma';
            }}

            QTableWidget::item {{
                padding: 6px;
                border-bottom: 1px solid {self.colors['light']};
            }}

            QTableWidget::item:selected {{
                background: {self.colors['secondary']};
                color: white;
            }}

            QHeaderView::section {{
                background: {self.colors['primary']};
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
                font-family: 'Tahoma';
            }}

            QLineEdit, QComboBox, QDateEdit {{
                border: 2px solid {self.colors['light']};
                border-radius: 5px;
                padding: 6px;
                font-family: 'Tahoma';
                background: {self.colors['white']};
            }}

            QLineEdit:focus, QComboBox:focus, QDateEdit:focus {{
                border-color: {self.colors['secondary']};
            }}

            QLabel {{
                font-family: 'Tahoma';
                color: {self.colors['primary']};
            }}
        """)



    def scale_value(self, base_value, min_value=None):
        """تحجيم قيمة بشكل متناسق مع حد أدنى"""
        # إزالة هذه الدالة لأنها لم تعد مستخدمة
        return base_value

    def center_dialog(self):
        """توسيط نافذة الحوار بدقة"""
        from PyQt5.QtWidgets import QApplication
        # معالجة الأحداث أولاً لضمان تحديث الأبعاد
        QApplication.processEvents()

        screen = QApplication.desktop().screenGeometry()

        # الحصول على أبعاد النافذة الحالية
        window_width = self.width()
        window_height = self.height()

        # حساب الموقع المركزي
        x = (screen.width() - window_width) // 2
        y = (screen.height() - window_height) // 2

        # التأكد من أن النافذة لا تخرج عن حدود الشاشة
        x = max(0, min(x, screen.width() - window_width))
        y = max(0, min(y, screen.height() - window_height))

        self.move(x, y)

    def setup_ui(self):
        """تصميم وفق التعليمات التقنية الدقيقة"""
        # QVBoxLayout رئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 0, 10, 10)  # تحريك العنوان للحافة العلوية
        main_layout.setSpacing(6)  # مسافة رأسية بين العناصر: 6px

        # 7️⃣ العنوان الرئيسي
        self.create_title_section(main_layout)

        # 2️⃣ منطقة معلومات الفاتورة
        self.create_invoice_info_section(main_layout)

        # 3️⃣ إدارة المواد المشتراة
        self.create_materials_input_section(main_layout)

        # 4️⃣ جدول المواد المضافة
        self.create_materials_table_section(main_layout)

        # 5️⃣ منطقة المبالغ المالية
        self.create_financial_amounts_section(main_layout)

        # 6️⃣ أزرار التحكم
        self.create_control_buttons_section(main_layout)

    def create_title_section(self, parent_layout):
        """1️⃣ العنوان الرئيسي - محرك للأعلى ومكبر بنسبة 25%"""
        title_label = QLabel("إنشاء فاتورة مشتريات جديدة")
        title_label.setFont(QFont("Tahoma", 16, QFont.Bold))  # زيادة حجم الخط بنسبة 25%
        title_label.setAlignment(Qt.AlignCenter)  # محاذاة وسط النافذة
        title_label.setFixedHeight(44)  # زيادة الارتفاع بنسبة 25% (35 + 25% = 44)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {self.colors['primary']};
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {self.colors['light']}, stop:1 {self.colors['white']});
                padding: 10px;
                border-radius: 8px;
                border: 2px solid {self.colors['secondary']};
                font-weight: bold;
                margin-top: 0px;
            }}
        """)
        parent_layout.addWidget(title_label)
        parent_layout.addSpacing(10)  # مسافة بعد العنوان

    def create_invoice_info_section(self, parent_layout):
        """2️⃣ معلومات الفاتورة - أبعاد مصغرة"""
        # QHBoxLayout للترتيب الأفقي
        info_layout = QHBoxLayout()
        info_layout.setSpacing(6)  # تقليل المسافة بين الحقول إلى 6px

        # مورد: ComboBox
        supplier_label = QLabel("المورد:")
        supplier_label.setFont(QFont("Tahoma", 11))
        info_layout.addWidget(supplier_label)

        self.supplier_combo = QComboBox()
        self.supplier_combo.setFixedHeight(27)  # الارتفاع: 27px
        self.supplier_combo.setFixedWidth(140)  # العرض: 140px
        self.supplier_combo.setFont(QFont("Tahoma", 11))  # حجم الخط: 11pt
        self.supplier_combo.setStyleSheet("padding: 3px; padding-left: 5px;")  # ضبط الهوامش الداخلية لظهور النص
        info_layout.addWidget(self.supplier_combo)

        info_layout.addSpacing(6)  # مسافة 6px

        # رقم الفاتورة: LineEdit (مغلق)
        invoice_num_label = QLabel("رقم الفاتورة:")
        invoice_num_label.setFont(QFont("Tahoma", 11))
        info_layout.addWidget(invoice_num_label)

        self.invoice_number_edit = QLineEdit()
        self.invoice_number_edit.setFixedHeight(27)  # الارتفاع: 27px
        self.invoice_number_edit.setFixedWidth(140)  # العرض: 140px
        self.invoice_number_edit.setFont(QFont("Tahoma", 11))  # حجم الخط: 11pt
        self.invoice_number_edit.setReadOnly(True)  # مغلق
        self.invoice_number_edit.setStyleSheet("background: #f8f9fa; color: #6c757d; padding: 3px; padding-left: 5px;")  # ضبط الهوامش الداخلية لظهور النص
        info_layout.addWidget(self.invoice_number_edit)

        info_layout.addSpacing(6)  # مسافة 6px

        # تاريخ الفاتورة: DateEdit
        date_label = QLabel("تاريخ الفاتورة:")
        date_label.setFont(QFont("Tahoma", 11))
        info_layout.addWidget(date_label)

        self.invoice_date_edit = QDateEdit()
        self.invoice_date_edit.setFixedHeight(27)  # الارتفاع: 27px
        self.invoice_date_edit.setFixedWidth(140)  # العرض: 140px
        self.invoice_date_edit.setFont(QFont("Tahoma", 11))  # حجم الخط: 11pt
        self.invoice_date_edit.setDate(QDate.currentDate())
        self.invoice_date_edit.setCalendarPopup(True)
        self.invoice_date_edit.setStyleSheet("padding: 3px; padding-left: 5px;")  # ضبط الهوامش الداخلية لظهور النص
        info_layout.addWidget(self.invoice_date_edit)

        # إضافة مساحة كبيرة لدفع زر الإضافة إلى اليسار (مقابل زر الإنهاء)
        info_layout.addStretch()  # ملء المساحة المتبقية أولاً

        # زر إضافة: مقابل زر الإنهاء في الأسفل
        self.add_material_btn = QPushButton("إضافة")
        self.add_material_btn.setFixedHeight(27)  # نفس ارتفاع الحقول
        self.add_material_btn.setFixedWidth(80)  # العرض: 80px (نفس عرض زر الإنهاء)
        self.add_material_btn.setFont(QFont("Tahoma", 11, QFont.Bold))  # حجم الخط: 11pt
        self.add_material_btn.setStyleSheet(f"""
            QPushButton {{
                background: {self.colors['success']};
                color: white;
                border: none;
                border-radius: 5px;
                padding: 6px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: {self.colors['primary']};
            }}
        """)
        self.add_material_btn.clicked.connect(self.test_add_material)
        info_layout.addWidget(self.add_material_btn)
        parent_layout.addLayout(info_layout)

    def test_add_material(self):
        """دالة إضافة المواد - خطوة بخطوة"""
        try:
            print("الخطوة 1: بدء عملية الإضافة")

            # الخطوة 1: التحقق من اختيار المادة
            material_text = self.material_combo.currentText()
            print(f"الخطوة 2: المادة المختارة = {material_text}")

            if not material_text or material_text == "اختر المادة":
                QMessageBox.warning(self, "تحذير", "يرجى اختيار المادة")
                return

            print("الخطوة 3: تم اختيار المادة بنجاح")

            # الخطوة 2: قراءة الكمية والسعر
            quantity_text = self.quantity_edit.text()
            price_text = self.unit_price_edit.text()
            print(f"الخطوة 4: الكمية = {quantity_text}, السعر = {price_text}")

            # الخطوة 3: تحويل القيم
            try:
                quantity = float(quantity_text)
                unit_price = float(price_text)
                print(f"الخطوة 5: تحويل ناجح - الكمية = {quantity}, السعر = {unit_price}")
            except ValueError as ve:
                print(f"خطأ في التحويل: {ve}")
                QMessageBox.warning(self, "تحذير", "يرجى إدخال قيم رقمية صحيحة")
                return

            # الخطوة 4: التحقق من القيم
            if quantity <= 0 or unit_price <= 0:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال قيم أكبر من الصفر")
                return

            print("الخطوة 6: القيم صحيحة")

            # الخطوة 5: إنشاء العنصر
            unit = self.unit_combo.currentText()
            total_price = quantity * unit_price

            item = {
                'material_name': material_text,
                'quantity': quantity,
                'unit': unit,
                'unit_price': unit_price,
                'total_price': total_price
            }

            print(f"الخطوة 7: تم إنشاء العنصر = {item}")

            # الخطوة 6: إضافة للقائمة
            self.invoice_items.append(item)
            print(f"الخطوة 8: تم إضافة العنصر للقائمة. العدد الحالي = {len(self.invoice_items)}")

            # الخطوة 7: تحديث الجدول
            try:
                self.update_items_table()
                print("الخطوة 9: تم تحديث الجدول بنجاح")
            except Exception as table_error:
                print(f"خطأ في تحديث الجدول: {table_error}")

            # الخطوة 8: حساب المجموع
            try:
                total = sum(item['total_price'] for item in self.invoice_items)
                self.total_amount_edit.setText(f"{total:.2f}")
                print(f"الخطوة 10: تم حساب المجموع = {total} وتم تحديث الحقل")
            except Exception as calc_error:
                print(f"خطأ في حساب المجموع: {calc_error}")

            # الخطوة 9: إعادة تعيين الحقول
            try:
                self.material_combo.setCurrentIndex(0)
                self.quantity_edit.setText("1.00")
                self.unit_price_edit.setText("0.00")
                print("الخطوة 11: تم إعادة تعيين الحقول")
            except Exception as reset_error:
                print(f"خطأ في إعادة تعيين الحقول: {reset_error}")

            QMessageBox.information(self, "نجح", f"تم إضافة {material_text} بنجاح!")

        except Exception as e:
            print(f"خطأ عام في test_add_material: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"خطأ: {str(e)}")

    def create_materials_input_section(self, parent_layout):
        """3️⃣ إدارة المواد - أبعاد مصغرة"""
        # QHBoxLayout للترتيب الأفقي
        materials_layout = QHBoxLayout()
        materials_layout.setSpacing(6)  # تقليل المسافة بين الحقول إلى 6px

        # مادة: ComboBox
        material_label = QLabel("المادة:")
        material_label.setFont(QFont("Tahoma", 11))
        materials_layout.addWidget(material_label)

        self.material_combo = QComboBox()
        self.material_combo.setFixedHeight(27)  # الارتفاع: 27px
        self.material_combo.setFixedWidth(140)  # العرض: 140px
        self.material_combo.setFont(QFont("Tahoma", 11))  # حجم الخط: 11pt
        self.material_combo.setStyleSheet("padding: 3px; padding-left: 5px;")  # ضبط الهوامش الداخلية لظهور النص
        materials_layout.addWidget(self.material_combo)

        materials_layout.addSpacing(6)  # مسافة 6px

        # الكمية: QLineEdit
        quantity_label = QLabel("الكمية:")
        quantity_label.setFont(QFont("Tahoma", 11))
        materials_layout.addWidget(quantity_label)

        self.quantity_edit = QLineEdit()
        self.quantity_edit.setFixedHeight(27)  # الارتفاع: 27px
        self.quantity_edit.setFixedWidth(140)  # العرض: 140px
        self.quantity_edit.setFont(QFont("Tahoma", 11))  # حجم الخط: 11pt
        self.quantity_edit.setText("1.00")
        self.quantity_edit.setAlignment(Qt.AlignCenter)
        self.quantity_edit.setStyleSheet("padding: 3px; padding-left: 5px;")  # ضبط الهوامش الداخلية لظهور النص
        materials_layout.addWidget(self.quantity_edit)

        materials_layout.addSpacing(6)  # مسافة 6px

        # الوحدة: ComboBox
        unit_label = QLabel("الوحدة:")
        unit_label.setFont(QFont("Tahoma", 11))
        materials_layout.addWidget(unit_label)

        self.unit_combo = QComboBox()
        self.unit_combo.setFixedHeight(27)  # الارتفاع: 27px
        self.unit_combo.setFixedWidth(140)  # العرض: 140px
        self.unit_combo.setFont(QFont("Tahoma", 11))  # حجم الخط: 11pt
        self.unit_combo.addItems(["كيلو", "غرام", "لتر", "قطعة", "علبة"])
        self.unit_combo.setStyleSheet("padding: 3px; padding-left: 5px;")  # ضبط الهوامش الداخلية لظهور النص
        materials_layout.addWidget(self.unit_combo)

        materials_layout.addSpacing(6)  # مسافة 6px

        # سعر الوحدة: QLineEdit
        price_label = QLabel("سعر الوحدة:")
        price_label.setFont(QFont("Tahoma", 11))
        materials_layout.addWidget(price_label)

        self.unit_price_edit = QLineEdit()
        self.unit_price_edit.setFixedHeight(27)  # الارتفاع: 27px
        self.unit_price_edit.setFixedWidth(140)  # العرض: 140px
        self.unit_price_edit.setFont(QFont("Tahoma", 11))  # حجم الخط: 11pt
        self.unit_price_edit.setText("0.00")
        self.unit_price_edit.setAlignment(Qt.AlignCenter)
        self.unit_price_edit.setStyleSheet("padding: 3px; padding-left: 5px;")  # ضبط الهوامش الداخلية لظهور النص
        materials_layout.addWidget(self.unit_price_edit)

        materials_layout.addStretch()  # ملء المساحة المتبقية
        parent_layout.addLayout(materials_layout)

    def create_materials_table_section(self, parent_layout):
        """4️⃣ جدول المواد - أبعاد مصغرة"""
        # مسافة 6px قبل الجدول
        parent_layout.addSpacing(6)

        # جدول QTableWidget بعدد أعمدة ثابت
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(5)
        self.items_table.setHorizontalHeaderLabels(["المادة", "الكمية", "الوحدة", "السعر", "الإجمالي"])

        # ضبط الأعمدة بالتساوي
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)  # توزيع متساوي

        # إخفاء شريط التمرير إلا عند الحاجة
        self.items_table.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.items_table.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # تحديد ارتفاع الجدول
        self.items_table.setFixedHeight(100)

        # تنسيق الجدول
        self.items_table.setAlternatingRowColors(True)
        self.items_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.items_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.items_table.setFont(QFont("Tahoma", 11))  # حجم الخط: 11pt

        # ارتفاع الصفوف: 24px
        self.items_table.verticalHeader().setDefaultSectionSize(24)

        # تنسيق رؤوس الأعمدة
        self.items_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: 2px solid #dee2e6;
                border-radius: 3px;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                color: #495057;
                padding: 6px;
                border: 1px solid #dee2e6;
                font-weight: bold;
                font-size: 11px;
                height: 24px;
            }
            QTableWidget::item {
                padding: 4px;
                font-size: 11px;
            }
        """)

        # ربط أحداث التحديد
        self.items_table.itemSelectionChanged.connect(self.on_table_selection_changed)

        parent_layout.addWidget(self.items_table)

        # مسافة 6px بعد الجدول
        parent_layout.addSpacing(6)

        # تهيئة قائمة المواد
        self.invoice_items = []

    def create_financial_amounts_section(self, parent_layout):
        """5️⃣ المبالغ المالية - أبعاد مصغرة"""
        # QHBoxLayout للترتيب الأفقي
        amounts_layout = QHBoxLayout()
        amounts_layout.setSpacing(6)  # مسافة 6px بين العناصر

        # المبلغ الإجمالي: QLineEdit (مغلق)
        total_label = QLabel("المبلغ الإجمالي:")
        total_label.setFont(QFont("Tahoma", 11))
        amounts_layout.addWidget(total_label)

        self.total_amount_edit = QLineEdit()
        self.total_amount_edit.setFixedHeight(27)  # الارتفاع: 27px
        self.total_amount_edit.setFixedWidth(140)  # العرض: 140px
        self.total_amount_edit.setFont(QFont("Tahoma", 11))  # حجم الخط: 11pt
        self.total_amount_edit.setText("0.00")
        self.total_amount_edit.setAlignment(Qt.AlignCenter)
        self.total_amount_edit.setReadOnly(True)  # مغلق
        self.total_amount_edit.setStyleSheet("background: #e9ecef; color: #495057; font-weight: bold; padding: 3px; padding-left: 5px;")  # ضبط الهوامش الداخلية لظهور النص
        amounts_layout.addWidget(self.total_amount_edit)

        amounts_layout.addSpacing(6)  # مسافة 6px

        # المبلغ المدفوع: QLineEdit
        paid_label = QLabel("المبلغ المدفوع:")
        paid_label.setFont(QFont("Tahoma", 11))
        amounts_layout.addWidget(paid_label)

        self.paid_amount_edit = QLineEdit()
        self.paid_amount_edit.setFixedHeight(27)  # الارتفاع: 27px
        self.paid_amount_edit.setFixedWidth(140)  # العرض: 140px
        self.paid_amount_edit.setFont(QFont("Tahoma", 11))  # حجم الخط: 11pt
        self.paid_amount_edit.setText("0.00")
        self.paid_amount_edit.setAlignment(Qt.AlignCenter)
        self.paid_amount_edit.setStyleSheet("padding: 3px; padding-left: 5px;")  # ضبط الهوامش الداخلية لظهور النص
        # إزالة الربط المؤقت لتجنب المشكلة
        # self.paid_amount_edit.textChanged.connect(self.calculate_remaining)
        amounts_layout.addWidget(self.paid_amount_edit)

        amounts_layout.addSpacing(6)  # مسافة 6px

        # طريقة الدفع: ComboBox
        payment_label = QLabel("طريقة الدفع:")
        payment_label.setFont(QFont("Tahoma", 11))
        amounts_layout.addWidget(payment_label)

        self.payment_method_combo = QComboBox()
        self.payment_method_combo.setFixedHeight(27)  # الارتفاع: 27px
        self.payment_method_combo.setFixedWidth(140)  # العرض: 140px
        self.payment_method_combo.setFont(QFont("Tahoma", 11))  # حجم الخط: 11pt
        self.payment_method_combo.addItems(["نقداً", "شيك", "تحويل بنكي", "آجل"])
        self.payment_method_combo.setStyleSheet("padding: 3px; padding-left: 5px;")  # ضبط الهوامش الداخلية لظهور النص
        amounts_layout.addWidget(self.payment_method_combo)

        amounts_layout.addStretch()  # ملء المساحة المتبقية
        parent_layout.addLayout(amounts_layout)

    def create_control_buttons_section(self, parent_layout):
        """6️⃣ أزرار التحكم - أبعاد مصغرة"""
        # QHBoxLayout للترتيب الأفقي
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)  # ضبط مسافة 10px بين كل زر

        buttons_layout.addStretch()  # دفع الأزرار لليمين

        # حفظ الفاتورة - أخضر
        save_btn = QPushButton("حفظ الفاتورة")
        save_btn.setFixedWidth(80)  # العرض: 80px
        save_btn.setFixedHeight(25)  # الارتفاع: 25px
        save_btn.setFont(QFont("Tahoma", 11, QFont.Bold))  # حجم الخط: 11pt
        save_btn.setStyleSheet(f"""
            QPushButton {{
                background: {self.colors['success']};
                color: white;
                border: none;
                border-radius: 5px;
                padding: 6px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: {self.colors['primary']};
            }}
        """)
        save_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(save_btn)

        buttons_layout.addSpacing(10)  # مسافة 10px

        # معاينة - أزرق
        preview_btn = QPushButton("معاينة")
        preview_btn.setFixedWidth(80)  # العرض: 80px
        preview_btn.setFixedHeight(25)  # الارتفاع: 25px
        preview_btn.setFont(QFont("Tahoma", 11, QFont.Bold))  # حجم الخط: 11pt
        preview_btn.setStyleSheet(f"""
            QPushButton {{
                background: {self.colors['info']};
                color: white;
                border: none;
                border-radius: 5px;
                padding: 6px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: {self.colors['secondary']};
            }}
        """)
        preview_btn.clicked.connect(self.test_preview)
        buttons_layout.addWidget(preview_btn)

        buttons_layout.addSpacing(10)  # مسافة 10px

        # إنهاء - أحمر
        cancel_btn = QPushButton("إنهاء")
        cancel_btn.setFixedWidth(80)  # العرض: 80px
        cancel_btn.setFixedHeight(25)  # الارتفاع: 25px
        cancel_btn.setFont(QFont("Tahoma", 11, QFont.Bold))  # حجم الخط: 11pt
        cancel_btn.setStyleSheet(f"""
            QPushButton {{
                background: {self.colors['accent']};
                color: white;
                border: none;
                border-radius: 5px;
                padding: 6px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: {self.colors['dark']};
            }}
        """)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        parent_layout.addLayout(buttons_layout)

    def add_material_to_invoice(self):
        """إضافة مادة للفاتورة - نسخة مبسطة"""
        try:
            print("بدء عملية إضافة المادة...")  # للتشخيص

            # التحقق من اختيار المادة
            material_text = self.material_combo.currentText()
            if not material_text or material_text == "اختر المادة":
                QMessageBox.warning(self, "تحذير", "يرجى اختيار المادة")
                return

            print(f"المادة المختارة: {material_text}")  # للتشخيص

            # التحقق من الكمية والسعر
            try:
                quantity = float(self.quantity_edit.text())
                unit_price = float(self.unit_price_edit.text())
            except ValueError:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال قيم رقمية صحيحة")
                return

            if quantity <= 0 or unit_price <= 0:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال قيم أكبر من الصفر")
                return

            print(f"الكمية: {quantity}, السعر: {unit_price}")  # للتشخيص

            # إنشاء عنصر المادة
            unit = self.unit_combo.currentText()
            total_price = quantity * unit_price

            item = {
                'material_name': material_text,
                'quantity': quantity,
                'unit': unit,
                'unit_price': unit_price,
                'total_price': total_price
            }

            print(f"العنصر المنشأ: {item}")  # للتشخيص

            # إضافة للقائمة
            self.invoice_items.append(item)
            print(f"عدد العناصر في القائمة: {len(self.invoice_items)}")  # للتشخيص

            # تحديث الجدول
            self.update_items_table()
            print("تم تحديث الجدول")  # للتشخيص

            # حساب المجموع
            self.calculate_total()
            print("تم حساب المجموع")  # للتشخيص

            # إعادة تعيين الحقول
            self.material_combo.setCurrentIndex(0)
            self.quantity_edit.setText("1.00")
            self.unit_price_edit.setText("0.00")

            QMessageBox.information(self, "نجح", "تم إضافة المادة بنجاح")
            print("تمت العملية بنجاح")  # للتشخيص

        except Exception as e:
            print(f"خطأ في add_material_to_invoice: {str(e)}")  # للتشخيص
            import traceback
            traceback.print_exc()  # طباعة تفاصيل الخطأ
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة المادة: {str(e)}")

    def calculate_remaining(self):
        """حساب المبلغ المتبقي - نسخة آمنة"""
        try:
            total_text = self.total_amount_edit.text().strip()
            paid_text = self.paid_amount_edit.text().strip()

            # التحقق من وجود قيم
            if not total_text:
                total_text = "0"
            if not paid_text:
                paid_text = "0"

            total = float(total_text)
            paid = float(paid_text)
            remaining = total - paid

            print(f"المجموع: {total}, المدفوع: {paid}, المتبقي: {remaining}")

        except Exception as e:
            print(f"خطأ في حساب المبلغ المتبقي: {e}")
            pass

    def test_preview(self):
        """دالة اختبار زر المعاينة"""
        try:
            print("تم الضغط على زر المعاينة!")
            QMessageBox.information(self, "اختبار", "زر المعاينة يعمل!")
        except Exception as e:
            print(f"خطأ في test_preview: {str(e)}")

    def calculate_total(self):
        """حساب المجموع الإجمالي"""
        try:
            total = sum(item['total_price'] for item in self.invoice_items)
            self.total_amount_edit.setText(f"{total:.2f}")
            print(f"تم حساب المجموع: {total}")  # للتشخيص

            # حساب المبلغ المتبقي بطريقة آمنة
            try:
                paid = float(self.paid_amount_edit.text() or "0")
                remaining = total - paid
                print(f"المبلغ المتبقي: {remaining}")
            except:
                print("تم تخطي حساب المبلغ المتبقي")

        except Exception as e:
            print(f"خطأ في calculate_total: {str(e)}")  # للتشخيص

    def calculate_remaining(self):
        """حساب المبلغ المتبقي"""
        total = self.total_amount_spin.value()
        paid = self.paid_amount_spin.value()
        remaining = total - paid

        self.remaining_amount_label.setText(f"{remaining:.2f} دج")

        # تلوين المبلغ المتبقي حسب الحالة
        if remaining > 0:
            self.remaining_amount_label.setStyleSheet("""
                background-color: #f8d7da;
                color: #721c24;
                font-weight: bold;
                border: 2px solid #f5c6cb;
                border-radius: 5px;
                padding: 5px;
            """)
        elif remaining == 0:
            self.remaining_amount_label.setStyleSheet("""
                background-color: #d4edda;
                color: #155724;
                font-weight: bold;
                border: 2px solid #c3e6cb;
                border-radius: 5px;
                padding: 5px;
            """)
        else:
            self.remaining_amount_label.setStyleSheet("""
                background-color: #d1ecf1;
                color: #0c5460;
                font-weight: bold;
                border: 2px solid #bee5eb;
                border-radius: 5px;
                padding: 5px;
            """)

    def update_items_table(self):
        """تحديث جدول المواد وفق التعديلات الدقيقة"""
        try:
            self.items_table.setRowCount(len(self.invoice_items))

            for row, item in enumerate(self.invoice_items):
                # المادة
                name_item = QTableWidgetItem(str(item['material_name']))
                name_item.setTextAlignment(Qt.AlignCenter)
                name_item.setFont(QFont("Tahoma", 11))  # حجم الخط: 11pt
                self.items_table.setItem(row, 0, name_item)

                # الكمية
                quantity_item = QTableWidgetItem(f"{float(item['quantity']):.2f}")
                quantity_item.setTextAlignment(Qt.AlignCenter)
                quantity_item.setFont(QFont("Tahoma", 11))  # حجم الخط: 11pt
                self.items_table.setItem(row, 1, quantity_item)

                # الوحدة
                unit_item = QTableWidgetItem(str(item['unit']))
                unit_item.setTextAlignment(Qt.AlignCenter)
                unit_item.setFont(QFont("Tahoma", 11))  # حجم الخط: 11pt
                self.items_table.setItem(row, 2, unit_item)

                # السعر
                price_item = QTableWidgetItem(f"{float(item['unit_price']):.2f}")
                price_item.setTextAlignment(Qt.AlignCenter)
                price_item.setFont(QFont("Tahoma", 11))  # حجم الخط: 11pt
                self.items_table.setItem(row, 3, price_item)

                # الإجمالي
                total_item = QTableWidgetItem(f"{float(item['total_price']):.2f}")
                total_item.setTextAlignment(Qt.AlignCenter)
                total_item.setFont(QFont("Tahoma", 11))  # حجم الخط: 11pt
                total_item.setBackground(QColor("#e8f5e8"))  # خلفية خضراء فاتحة
                self.items_table.setItem(row, 4, total_item)

            print(f"تم تحديث الجدول بـ {len(self.invoice_items)} عنصر")  # للتشخيص

        except Exception as e:
            print(f"خطأ في update_items_table: {str(e)}")  # للتشخيص

    def on_table_selection_changed(self):
        """تفعيل/إلغاء تفعيل أزرار التعديل والحذف"""
        # يمكن إضافة أزرار التعديل والحذف لاحقاً إذا لزم الأمر
        pass

    def edit_selected_material(self):
        """تعديل المادة المحددة"""
        # يمكن تنفيذها لاحقاً
        pass

    def delete_selected_material(self):
        """حذف المادة المحددة"""
        # يمكن تنفيذها لاحقاً
        pass

    def load_materials(self):
        """تحميل قائمة المواد"""
        try:
            materials = db.execute_query("SELECT id, name, unit, price FROM raw_materials ORDER BY name")

            self.material_combo.clear()
            self.material_combo.addItem("اختر المادة", 0)

            for material in materials:
                display_text = f"{material['name']} ({material['unit']})"
                self.material_combo.addItem(display_text, material['id'])

            # ربط تغيير المادة بتحديث السعر
            self.material_combo.currentIndexChanged.connect(self.update_material_price)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل المواد: {str(e)}")

    def update_material_price(self):
        """تحديث سعر المادة عند اختيارها"""
        material_id = self.material_combo.currentData()
        if material_id and material_id != 0:
            try:
                material = db.execute_query("SELECT price FROM raw_materials WHERE id = ?", (material_id,))
                if material:
                    self.unit_price_edit.setText(f"{material[0]['price']:.2f}")
            except Exception as e:
                pass  # تجاهل الأخطاء في التحديث التلقائي

    def preview_invoice(self):
        """معاينة الفاتورة - نسخة مبسطة"""
        try:
            print("بدء معاينة الفاتورة...")

            if not self.invoice_items:
                QMessageBox.warning(self, "تحذير", "لا توجد مواد في الفاتورة للمعاينة")
                return

            print("إنشاء نص المعاينة...")

            # نص بسيط جداً
            preview_text = "معاينة الفاتورة\n================\n\n"

            # إضافة المواد
            preview_text += "المواد المشتراة:\n"
            for i, item in enumerate(self.invoice_items, 1):
                preview_text += f"{i}. {item['material_name']}\n"

            # إضافة المجموع
            total = self.total_amount_edit.text()
            preview_text += f"\nالمبلغ الإجمالي: {total} دج"

            print("عرض المعاينة...")
            QMessageBox.information(self, "معاينة", preview_text)
            print("تمت المعاينة بنجاح")

        except Exception as e:
            print(f"خطأ في المعاينة: {str(e)}")
            QMessageBox.critical(self, "خطأ", "خطأ في المعاينة")

    def create_amounts_section(self, parent_layout):
        """إنشاء قسم المبالغ المالية"""
        group = QGroupBox("المبالغ المالية")
        group.setStyleSheet("QGroupBox { font-weight: bold; padding-top: 10px; }")
        layout = QGridLayout(group)
        layout.setSpacing(10)

        # الصف الأول: المبلغ الإجمالي والمدفوع
        layout.addWidget(QLabel("المبلغ الإجمالي:"), 0, 0)
        self.total_amount_label = QLabel("0.00 دج")
        self.total_amount_label.setFixedHeight(self.field_height)
        self.total_amount_label.setFixedWidth(self.field_width)
        self.total_amount_label.setStyleSheet("background: #ECF0F1; padding: 5px; border: 1px solid #BDC3C7; border-radius: 3px; font-weight: bold; color: #2C3E50;")
        layout.addWidget(self.total_amount_label, 0, 1)

        layout.addWidget(QLabel("المبلغ المدفوع:"), 0, 2)
        self.paid_amount_spin = QDoubleSpinBox()
        self.paid_amount_spin.setFixedHeight(self.field_height)
        self.paid_amount_spin.setFixedWidth(self.field_width)
        self.paid_amount_spin.setRange(0, 999999.99)
        self.paid_amount_spin.setDecimals(2)
        self.paid_amount_spin.setSuffix(" دج")
        self.paid_amount_spin.valueChanged.connect(self.calculate_remaining)
        layout.addWidget(self.paid_amount_spin, 0, 3)

        # الصف الثاني: المبلغ المتبقي وطريقة الدفع
        layout.addWidget(QLabel("المبلغ المتبقي:"), 1, 0)
        self.remaining_amount_label = QLabel("0.00 دج")
        self.remaining_amount_label.setFixedHeight(self.field_height)
        self.remaining_amount_label.setFixedWidth(self.field_width)
        self.remaining_amount_label.setStyleSheet("background: #ECF0F1; padding: 5px; border: 1px solid #BDC3C7; border-radius: 3px; font-weight: bold; color: #E74C3C;")
        layout.addWidget(self.remaining_amount_label, 1, 1)

        layout.addWidget(QLabel("طريقة الدفع:"), 1, 2)
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.setFixedHeight(self.field_height)
        self.payment_method_combo.setFixedWidth(self.field_width)
        self.payment_method_combo.addItems(["نقداً", "شيك", "تحويل بنكي", "آجل"])
        layout.addWidget(self.payment_method_combo, 1, 3)

        parent_layout.addWidget(group)

    def create_notes_section(self, parent_layout):
        """إنشاء قسم الملاحظات"""
        group = QGroupBox("ملاحظات")
        group.setStyleSheet("QGroupBox { font-weight: bold; padding-top: 10px; }")
        layout = QVBoxLayout(group)

        self.notes_edit = QTextEdit()
        self.notes_edit.setFixedHeight(50)
        self.notes_edit.setPlaceholderText("أدخل أي ملاحظات إضافية...")
        layout.addWidget(self.notes_edit)

        parent_layout.addWidget(group)



    def preview_invoice(self):
        """معاينة الفاتورة"""
        if not self.invoice_items:
            QMessageBox.warning(self, "تحذير", "لا توجد مواد في الفاتورة للمعاينة")
            return

        # إنشاء نص المعاينة
        preview_text = f"""
معاينة الفاتورة
================

رقم الفاتورة: {self.invoice_number_edit.text()}
التاريخ: {self.invoice_date_edit.date().toString('yyyy-MM-dd')}
المورد: {self.supplier_combo.currentText()}

المواد المشتراة:
----------------
"""

        for i, item in enumerate(self.invoice_items, 1):
            preview_text += f"{i}. {item['material_name']} - {item['quantity']:.2f} {item['unit']} × {item['unit_price']:.2f} دج = {item['total_price']:.2f} دج\n"

        preview_text += f"""
----------------
المبلغ الإجمالي: {self.total_amount_spin.value():.2f} دج
المبلغ المدفوع: {self.paid_amount_spin.value():.2f} دج
المبلغ المتبقي: {self.total_amount_spin.value() - self.paid_amount_spin.value():.2f} دج
"""

        QMessageBox.information(self, "معاينة الفاتورة", preview_text)



    def calculate_total(self):
        """حساب المجموع الإجمالي مع تحديث فوري"""
        total = sum(item['total_price'] for item in self.invoice_items)
        # تحديث المبلغ الإجمالي في الحقل
        self.total_amount_spin.setValue(total)
        # حساب المبلغ المتبقي
        self.calculate_remaining()

    def on_table_selection_changed(self):
        """تفعيل/إلغاء تفعيل أزرار التعديل والحذف حسب التحديد"""
        has_selection = len(self.items_table.selectedItems()) > 0
        self.edit_material_btn.setEnabled(has_selection)
        self.delete_material_btn.setEnabled(has_selection)

    def edit_selected_material(self):
        """تعديل المادة المحددة"""
        current_row = self.items_table.currentRow()
        if current_row < 0 or current_row >= len(self.invoice_items):
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مادة للتعديل")
            return

        item = self.invoice_items[current_row]

        # ملء الحقول بالبيانات الحالية
        for i in range(self.material_combo.count()):
            if self.material_combo.itemData(i) == item['material_id']:
                self.material_combo.setCurrentIndex(i)
                break

        self.quantity_spin.setValue(item['quantity'])
        self.unit_price_spin.setValue(item['unit_price'])

        # حذف المادة من القائمة مؤقتاً
        self.invoice_items.pop(current_row)
        self.update_items_table()
        self.calculate_total()

        # تركيز على زر الإضافة
        self.add_material_btn.setText("تحديث المادة")
        self.add_material_btn.setStyleSheet("background: #F39C12; color: white; font-weight: bold; border-radius: 5px;")

    def delete_selected_material(self):
        """حذف المادة المحددة"""
        current_row = self.items_table.currentRow()
        if current_row < 0 or current_row >= len(self.invoice_items):
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مادة للحذف")
            return

        item = self.invoice_items[current_row]

        # تأكيد الحذف
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف المادة '{item['material_name']}'؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.invoice_items.pop(current_row)
            self.update_items_table()
            self.calculate_total()
            QMessageBox.information(self, "نجح", "تم حذف المادة بنجاح")

    def reset_add_button(self):
        """إعادة تعيين زر الإضافة للحالة العادية"""
        self.add_material_btn.setText("إضافة المادة")
        self.add_material_btn.setStyleSheet("background: #27AE60; color: white; font-weight: bold; border-radius: 5px;")

        # قسم إضافة المواد
        materials_group = QGroupBox("إضافة المواد المشتراة")
        materials_layout = QVBoxLayout(materials_group)

        # أدوات إضافة المواد
        add_material_layout = QHBoxLayout()

        self.material_combo = QComboBox()
        self.material_combo.setMinimumWidth(self.scale_value(200, 150))  # حد أدنى 150
        self.material_combo.setMinimumHeight(25)  # حد أدنى للارتفاع
        add_material_layout.addWidget(QLabel("المادة:"))
        add_material_layout.addWidget(self.material_combo)

        self.quantity_spin = QDoubleSpinBox()
        self.quantity_spin.setRange(0.01, 999999.99)
        self.quantity_spin.setDecimals(2)
        self.quantity_spin.setValue(1.0)
        self.quantity_spin.setMinimumHeight(25)  # حد أدنى للارتفاع
        add_material_layout.addWidget(QLabel("الكمية:"))
        add_material_layout.addWidget(self.quantity_spin)

        self.unit_price_spin = QDoubleSpinBox()
        self.unit_price_spin.setRange(0.01, 999999.99)
        self.unit_price_spin.setDecimals(2)
        self.unit_price_spin.setSuffix(" دج")
        self.unit_price_spin.setMinimumHeight(25)  # حد أدنى للارتفاع
        add_material_layout.addWidget(QLabel("سعر الوحدة:"))
        add_material_layout.addWidget(self.unit_price_spin)

        add_material_btn = QPushButton("إضافة المادة")
        add_material_btn.clicked.connect(self.add_material_to_invoice)
        # تطبيق التحجيم المتناسق على الحشو والحواف
        padding_v = self.scale_value(8)
        padding_h = self.scale_value(15)
        radius = self.scale_value(5)
        add_material_btn.setStyleSheet(f"background: #27AE60; color: white; padding: {padding_v}px {padding_h}px; border-radius: {radius}px;")
        add_material_layout.addWidget(add_material_btn)

        materials_layout.addLayout(add_material_layout)

        # جدول المواد المضافة
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(6)
        self.items_table.setHorizontalHeaderLabels([
            "المادة", "الكمية", "الوحدة", "سعر الوحدة", "الإجمالي", "حذف"
        ])
        self.items_table.setMaximumHeight(self.scale_value(150))

        # تعديل عرض الأعمدة
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)

        materials_layout.addWidget(self.items_table)

        layout.addWidget(materials_group)

        # المبالغ المالية
        amounts_group = QGroupBox("المبالغ المالية")
        amounts_layout = QFormLayout(amounts_group)

        # المبلغ الإجمالي (محسوب تلقائياً)
        self.total_amount_label = QLabel("0.00 دج")
        self.total_amount_label.setStyleSheet("font-weight: bold; color: #2C3E50;")
        amounts_layout.addRow("المبلغ الإجمالي:", self.total_amount_label)

        # المبلغ المدفوع
        self.paid_amount_spin = QDoubleSpinBox()
        self.paid_amount_spin.setRange(0, 999999.99)
        self.paid_amount_spin.setDecimals(2)
        self.paid_amount_spin.setSuffix(" دج")
        self.paid_amount_spin.setMinimumHeight(25)  # حد أدنى للارتفاع
        amounts_layout.addRow("المبلغ المدفوع:", self.paid_amount_spin)

        # المبلغ المتبقي (محسوب تلقائياً)
        self.remaining_amount_label = QLabel("0.00 دج")
        self.remaining_amount_label.setStyleSheet("font-weight: bold; color: #E74C3C;")
        amounts_layout.addRow("المبلغ المتبقي:", self.remaining_amount_label)

        # ربط تغيير المبلغ المدفوع بحساب المتبقي
        self.paid_amount_spin.valueChanged.connect(self.calculate_remaining)

        layout.addWidget(amounts_group)

        # الملاحظات
        notes_group = QGroupBox("ملاحظات")
        notes_layout = QVBoxLayout(notes_group)

        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("أدخل أي ملاحظات إضافية")
        self.notes_edit.setMaximumHeight(self.scale_value(80))
        notes_layout.addWidget(self.notes_edit)

        layout.addWidget(notes_group)

        # أزرار التحكم
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)

        # تخصيص النصوص
        buttons.button(QDialogButtonBox.Ok).setText("حفظ الفاتورة")
        buttons.button(QDialogButtonBox.Cancel).setText("إلغاء")

        layout.addWidget(buttons)

    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        try:
            suppliers = db.execute_query("SELECT id, name FROM suppliers ORDER BY name")

            self.supplier_combo.clear()
            self.supplier_combo.addItem("اختر المورد", 0)

            for supplier in suppliers:
                self.supplier_combo.addItem(supplier['name'], supplier['id'])

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الموردين: {str(e)}")

    def load_materials(self):
        """تحميل قائمة المواد الأولية"""
        try:
            materials = db.execute_query("SELECT id, name, unit, price FROM raw_materials ORDER BY name")

            self.material_combo.clear()
            self.material_combo.addItem("اختر المادة", 0)

            for material in materials:
                display_text = f"{material['name']} ({material['unit']})"
                self.material_combo.addItem(display_text, material['id'])

            # ربط تغيير المادة بتحديث السعر
            self.material_combo.currentIndexChanged.connect(self.update_material_price)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل المواد الأولية: {str(e)}")

    def generate_invoice_number(self):
        """توليد رقم فاتورة تلقائي"""
        try:
            from datetime import datetime

            # جلب آخر رقم فاتورة
            last_invoice = db.execute_query("""
                SELECT invoice_number FROM purchase_invoices
                WHERE invoice_number LIKE 'PUR-%'
                ORDER BY id DESC LIMIT 1
            """)

            if last_invoice:
                # استخراج الرقم من آخر فاتورة
                last_number = last_invoice[0]['invoice_number']
                try:
                    # استخراج الرقم من النمط PUR-YYYY-XXXX
                    parts = last_number.split('-')
                    if len(parts) >= 3:
                        last_seq = int(parts[2])
                        new_seq = last_seq + 1
                    else:
                        new_seq = 1
                except:
                    new_seq = 1
            else:
                new_seq = 1

            # إنشاء رقم الفاتورة الجديد
            current_year = datetime.now().year
            invoice_number = f"PUR-{current_year}-{new_seq:04d}"

            self.invoice_number_edit.setText(invoice_number)

        except Exception as e:
            # في حالة الخطأ، استخدم رقم افتراضي
            from datetime import datetime
            current_year = datetime.now().year
            invoice_number = f"PUR-{current_year}-0001"
            self.invoice_number_edit.setText(invoice_number)

    def update_material_price(self):
        """تحديث سعر المادة عند اختيارها"""
        material_id = self.material_combo.currentData()
        if material_id and material_id != 0:
            try:
                material = db.execute_query("SELECT price FROM raw_materials WHERE id = ?", (material_id,))
                if material:
                    self.unit_price_spin.setValue(material[0]['price'])
            except Exception as e:
                pass  # تجاهل الأخطاء في التحديث التلقائي

    def add_material_to_invoice(self):
        """إضافة أو تحديث مادة في الفاتورة"""
        material_id = self.material_combo.currentData()
        if not material_id or material_id == 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار المادة")
            return

        quantity = self.quantity_spin.value()
        unit_price = self.unit_price_spin.value()

        if quantity <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال كمية صحيحة")
            return

        if unit_price <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال سعر صحيح")
            return

        try:
            # جلب معلومات المادة
            material = db.execute_query("SELECT name, unit FROM raw_materials WHERE id = ?", (material_id,))[0]

            # التحقق من عدم إضافة نفس المادة مرتين (إلا في حالة التعديل)
            if self.add_material_btn.text() == "إضافة المادة":
                for item in self.invoice_items:
                    if item['material_id'] == material_id:
                        QMessageBox.warning(self, "تحذير", "هذه المادة مضافة بالفعل. يمكنك تعديلها من الجدول.")
                        return

            # إضافة المادة للقائمة
            total_price = quantity * unit_price
            item = {
                'material_id': material_id,
                'material_name': material['name'],
                'quantity': quantity,
                'unit': material['unit'],
                'unit_price': unit_price,
                'total_price': total_price
            }

            self.invoice_items.append(item)
            self.update_items_table()
            self.calculate_total()

            # إعادة تعيين الحقول
            self.material_combo.setCurrentIndex(0)
            self.quantity_spin.setValue(1.0)
            self.unit_price_spin.setValue(0.0)

            # إعادة تعيين زر الإضافة
            self.reset_add_button()

            # رسالة نجاح
            action = "تحديث" if self.add_material_btn.text() == "تحديث المادة" else "إضافة"
            QMessageBox.information(self, "نجح", f"تم {action} المادة بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة المادة: {str(e)}")

    def update_items_table(self):
        """تحديث جدول المواد"""
        self.items_table.setRowCount(len(self.invoice_items))

        for row, item in enumerate(self.invoice_items):
            # إنشاء عناصر الجدول مع تنسيق
            name_item = QTableWidgetItem(item['material_name'])
            name_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 0, name_item)

            quantity_item = QTableWidgetItem(f"{item['quantity']:.2f}")
            quantity_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 1, quantity_item)

            unit_item = QTableWidgetItem(item['unit'])
            unit_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 2, unit_item)

            price_item = QTableWidgetItem(f"{item['unit_price']:.2f} دج")
            price_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 3, price_item)

            total_item = QTableWidgetItem(f"{item['total_price']:.2f} دج")
            total_item.setTextAlignment(Qt.AlignCenter)
            total_item.setBackground(QColor("#E8F5E8"))  # خلفية خضراء فاتحة
            self.items_table.setItem(row, 4, total_item)

        # تحديث حالة الأزرار
        self.on_table_selection_changed()

    def print_invoice(self):
        """طباعة الفاتورة"""
        if not self.invoice_items:
            QMessageBox.warning(self, "تحذير", "لا توجد مواد في الفاتورة للطباعة")
            return

        try:
            # إنشاء تقرير الفاتورة
            from datetime import datetime
            import os

            # إنشاء مجلد التقارير
            os.makedirs("reports", exist_ok=True)

            # اسم الملف
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            invoice_number = self.invoice_number_edit.text() or "جديدة"
            filename = f"reports/فاتورة_مشتريات_{invoice_number}_{timestamp}.txt"

            # محتوى التقرير
            supplier_name = self.supplier_combo.currentText() if self.supplier_combo.currentData() else "غير محدد"
            invoice_date = self.invoice_date_edit.date().toString('yyyy-MM-dd')
            status = self.status_combo.currentText()
            payment_method = self.payment_method_combo.currentText()

            report_content = f"""
===============================================
            فاتورة مشتريات
===============================================

رقم الفاتورة: {self.invoice_number_edit.text()}
تاريخ الفاتورة: {invoice_date}
تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
حالة الفاتورة: {status}

-----------------------------------------------
                بيانات المورد
-----------------------------------------------
اسم المورد: {supplier_name}

-----------------------------------------------
                تفاصيل المواد
-----------------------------------------------
"""

            total_amount = 0
            for i, item in enumerate(self.invoice_items, 1):
                report_content += f"{i}. {item['material_name']}\n"
                report_content += f"   الكمية: {item['quantity']:.2f} {item['unit']}\n"
                report_content += f"   سعر الوحدة: {item['unit_price']:.2f} دج\n"
                report_content += f"   الإجمالي: {item['total_price']:.2f} دج\n\n"
                total_amount += item['total_price']

            paid_amount = self.paid_amount_spin.value()
            remaining_amount = total_amount - paid_amount

            report_content += f"""
-----------------------------------------------
                ملخص المبالغ
-----------------------------------------------
المبلغ الإجمالي: {total_amount:.2f} دج
المبلغ المدفوع: {paid_amount:.2f} دج
المبلغ المتبقي: {remaining_amount:.2f} دج
طريقة الدفع: {payment_method}

الملاحظات: {self.notes_edit.toPlainText() or 'لا توجد ملاحظات'}

===============================================
        برنامج محاسبة مصنع الحلويات التقليدية
===============================================
"""

            # كتابة التقرير
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report_content)

            QMessageBox.information(self, "نجح", f"تم إنشاء تقرير الفاتورة:\n{filename}")

            # فتح الملف
            os.startfile(filename)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في طباعة الفاتورة: {str(e)}")

    def load_invoice_data(self):
        """تحميل بيانات فاتورة موجودة للتعديل"""
        if not self.invoice_id:
            return

        try:
            # جلب بيانات الفاتورة الرئيسية
            invoice_query = """
                SELECT pi.*, s.name as supplier_name
                FROM purchase_invoices pi
                LEFT JOIN suppliers s ON pi.supplier_id = s.id
                WHERE pi.id = ?
            """
            invoice_data = db.execute_query(invoice_query, (self.invoice_id,))

            if not invoice_data:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على الفاتورة")
                return

            invoice = invoice_data[0]

            # ملء البيانات الأساسية
            self.invoice_number_edit.setText(invoice['invoice_number'])
            self.invoice_date_edit.setDate(QDate.fromString(invoice['invoice_date'], 'yyyy-MM-dd'))
            self.paid_amount_spin.setValue(invoice['paid_amount'] or 0)
            self.notes_edit.setPlainText(invoice['notes'] or '')

            # تحديد المورد
            for i in range(self.supplier_combo.count()):
                if self.supplier_combo.itemData(i) == invoice['supplier_id']:
                    self.supplier_combo.setCurrentIndex(i)
                    break

            # جلب تفاصيل المواد
            items_query = """
                SELECT pii.*, rm.name as material_name, rm.unit
                FROM purchase_invoice_items pii
                LEFT JOIN raw_materials rm ON pii.material_id = rm.id
                WHERE pii.invoice_id = ?
            """
            items_data = db.execute_query(items_query, (self.invoice_id,))

            # إضافة المواد للقائمة
            self.invoice_items = []
            for item_data in items_data:
                item = {
                    'material_id': item_data['material_id'],
                    'material_name': item_data['material_name'],
                    'quantity': item_data['quantity'],
                    'unit': item_data['unit'],
                    'unit_price': item_data['unit_price'],
                    'total_price': item_data['total_price']
                }
                self.invoice_items.append(item)

            # تحديث الجدول والمجاميع
            self.update_items_table()
            self.calculate_total()

            # تغيير عنوان النافذة
            self.setWindowTitle(f"تعديل فاتورة المشتريات - {invoice['invoice_number']}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات الفاتورة: {str(e)}")

    def remove_material(self, row):
        """حذف مادة من الفاتورة"""
        if 0 <= row < len(self.invoice_items):
            self.invoice_items.pop(row)
            self.update_items_table()
            self.calculate_total()

    def calculate_total(self):
        """حساب المجموع الإجمالي"""
        total = sum(item['total_price'] for item in self.invoice_items)
        self.total_amount_label.setText(f"{total:.2f} دج")
        self.calculate_remaining()

    def calculate_remaining(self):
        """حساب المبلغ المتبقي"""
        total_text = self.total_amount_label.text().replace(" دج", "")
        try:
            total = float(total_text)
            paid = self.paid_amount_spin.value()
            remaining = total - paid

            self.remaining_amount_label.setText(f"{remaining:.2f} دج")

            # تلوين المبلغ المتبقي
            if remaining > 0:
                self.remaining_amount_label.setStyleSheet("font-weight: bold; color: #E74C3C;")
            else:
                self.remaining_amount_label.setStyleSheet("font-weight: bold; color: #27AE60;")
        except:
            self.remaining_amount_label.setText("0.00 دج")

    def get_data(self):
        """الحصول على البيانات المدخلة - نسخة مبسطة"""
        return {
            'supplier_id': self.supplier_combo.currentData(),
            'invoice_number': self.invoice_number_edit.text().strip(),
            'total_amount': self.total_amount_spin.value(),
            'paid_amount': self.paid_amount_spin.value(),
            'invoice_date': self.invoice_date_edit.date().toString('yyyy-MM-dd'),
            'notes': '',
            'status': 'مسودة',
            'payment_method': 'نقداً',
            'items': []  # سنضيف المواد لاحقاً
        }

    def accept(self):
        """التحقق من صحة البيانات قبل الحفظ - نسخة مبسطة"""
        if not self.supplier_combo.currentData() or self.supplier_combo.currentData() == 0:
            QMessageBox.warning(self, "خطأ", "يرجى اختيار المورد")
            self.supplier_combo.setFocus()
            return

        if not self.invoice_number_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال رقم الفاتورة")
            self.invoice_number_edit.setFocus()
            return

        total_amount = self.total_amount_spin.value()
        if total_amount <= 0:
            QMessageBox.warning(self, "خطأ", "المبلغ الإجمالي يجب أن يكون أكبر من صفر")
            return

        if self.paid_amount_spin.value() > total_amount:
            QMessageBox.warning(self, "خطأ", "المبلغ المدفوع لا يمكن أن يكون أكبر من المبلغ الإجمالي")
            self.paid_amount_spin.setFocus()
            return

        # رسالة تأكيد الحفظ
        reply = QMessageBox.question(
            self,
            "تأكيد الحفظ",
            f"هل أنت متأكد من حفظ الفاتورة؟\n\nرقم الفاتورة: {self.invoice_number_edit.text()}\nالمبلغ الإجمالي: {total_amount:.2f} دج",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )

        if reply == QMessageBox.Yes:
            super().accept()


class InvoiceViewDialog(QDialog):
    """نافذة عرض تفاصيل الفاتورة"""

    def __init__(self, parent=None, invoice_id=None):
        super().__init__(parent)
        self.invoice_id = invoice_id
        self.setWindowTitle("عرض تفاصيل الفاتورة")
        self.setFixedSize(700, 600)

        # توسيط النافذة
        self.center_dialog()

        self.setup_ui()
        self.load_invoice_data()

    def center_dialog(self):
        """توسيط نافذة الحوار"""
        if self.parent():
            parent_geometry = self.parent().geometry()
            x = parent_geometry.x() + (parent_geometry.width() - self.width()) // 2
            y = parent_geometry.y() + (parent_geometry.height() - self.height()) // 2
            self.move(x, y)

    def setup_ui(self):
        """إنشاء واجهة النافذة"""
        layout = QVBoxLayout(self)

        # عنوان النافذة
        title_label = QLabel("تفاصيل فاتورة المشتريات")
        title_label.setFont(QFont("Tahoma", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2C3E50; padding: 10px;")
        layout.addWidget(title_label)

        # معلومات الفاتورة
        info_group = QGroupBox("معلومات الفاتورة")
        info_layout = QFormLayout(info_group)

        self.invoice_number_label = QLabel()
        info_layout.addRow("رقم الفاتورة:", self.invoice_number_label)

        self.invoice_date_label = QLabel()
        info_layout.addRow("تاريخ الفاتورة:", self.invoice_date_label)

        self.supplier_name_label = QLabel()
        info_layout.addRow("اسم المورد:", self.supplier_name_label)

        self.supplier_phone_label = QLabel()
        info_layout.addRow("هاتف المورد:", self.supplier_phone_label)

        self.supplier_address_label = QLabel()
        info_layout.addRow("عنوان المورد:", self.supplier_address_label)

        layout.addWidget(info_group)

        # المبالغ المالية
        amounts_group = QGroupBox("المبالغ المالية")
        amounts_layout = QFormLayout(amounts_group)

        self.total_amount_label = QLabel()
        amounts_layout.addRow("المبلغ الإجمالي:", self.total_amount_label)

        self.paid_amount_label = QLabel()
        amounts_layout.addRow("المبلغ المدفوع:", self.paid_amount_label)

        self.remaining_amount_label = QLabel()
        amounts_layout.addRow("المبلغ المتبقي:", self.remaining_amount_label)

        layout.addWidget(amounts_group)

        # الملاحظات
        notes_group = QGroupBox("ملاحظات")
        notes_layout = QVBoxLayout(notes_group)

        self.notes_label = QLabel()
        self.notes_label.setWordWrap(True)
        self.notes_label.setStyleSheet("padding: 10px; background: #f8f9fa; border-radius: 5px;")
        notes_layout.addWidget(self.notes_label)

        layout.addWidget(notes_group)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        print_btn = QPushButton("طباعة الفاتورة")
        print_btn.clicked.connect(self.print_invoice)
        print_btn.setStyleSheet("background: #27AE60; color: white; padding: 10px 20px; border-radius: 5px;")

        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(self.close)
        close_btn.setStyleSheet("background: #95A5A6; color: white; padding: 10px 20px; border-radius: 5px;")

        buttons_layout.addStretch()
        buttons_layout.addWidget(print_btn)
        buttons_layout.addWidget(close_btn)

        layout.addLayout(buttons_layout)

    def load_invoice_data(self):
        """تحميل بيانات الفاتورة"""
        try:
            query = """
                SELECT pi.*, s.name as supplier_name, s.phone as supplier_phone, s.address as supplier_address
                FROM purchase_invoices pi
                LEFT JOIN suppliers s ON pi.supplier_id = s.id
                WHERE pi.id = ?
            """
            invoice_data = db.execute_query(query, (self.invoice_id,))

            if not invoice_data:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على الفاتورة")
                self.close()
                return

            invoice = invoice_data[0]

            # تحديث المعلومات
            self.invoice_number_label.setText(invoice['invoice_number'] or 'غير محدد')
            self.invoice_date_label.setText(invoice['invoice_date'] or 'غير محدد')
            self.supplier_name_label.setText(invoice['supplier_name'] or 'غير محدد')
            self.supplier_phone_label.setText(invoice['supplier_phone'] or 'غير محدد')
            self.supplier_address_label.setText(invoice['supplier_address'] or 'غير محدد')

            # تحديث المبالغ
            total_amount = invoice['total_amount'] or 0
            paid_amount = invoice['paid_amount'] or 0
            remaining_amount = total_amount - paid_amount

            self.total_amount_label.setText(f"{total_amount:.2f} دج")
            self.paid_amount_label.setText(f"{paid_amount:.2f} دج")
            self.remaining_amount_label.setText(f"{remaining_amount:.2f} دج")

            # تلوين المبلغ المتبقي
            if remaining_amount > 0:
                self.remaining_amount_label.setStyleSheet("color: #E74C3C; font-weight: bold;")
            else:
                self.remaining_amount_label.setStyleSheet("color: #27AE60; font-weight: bold;")

            # تحديث الملاحظات
            notes = invoice['notes'] or 'لا توجد ملاحظات'
            self.notes_label.setText(notes)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات الفاتورة: {str(e)}")

    def print_invoice(self):
        """طباعة الفاتورة"""
        try:
            # استدعاء دالة الطباعة من النافذة الأم
            if self.parent() and hasattr(self.parent(), 'generate_invoice_report'):
                self.parent().generate_invoice_report(self.invoice_id)
            else:
                QMessageBox.information(self, "معلومات", "وظيفة الطباعة غير متوفرة")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في طباعة الفاتورة: {str(e)}")
