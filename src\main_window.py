#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النافذة الرئيسية للبرنامج
واجهة رسومية حديثة بتصميم عربي
"""

import sys
import os
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QGridLayout, QPushButton, QLabel, QFrame, 
                             QApplication, QMessageBox, QSizePolicy)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QFont, QPixmap, QIcon, QPalette, QColor

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("برنامج محاسبة مصنع الحلويات التقليدية")

        # تحديد أبعاد مناسبة ومريحة للعين
        self.setFixedSize(900, 550)  # حجم مناسب ومتوسط
        
        # إعداد الألوان والتصميم
        self.setup_style()
        
        # إنشاء الواجهة
        self.setup_ui()
        
        # توسيط النافذة
        self.center_window()
    
    def setup_style(self):
        """إعداد الألوان والتصميم العام المحسن"""
        # ألوان البرنامج المحسنة
        self.colors = {
            'primary': '#1e3a8a',      # أزرق داكن أنيق
            'secondary': '#3b82f6',    # أزرق متوسط حديث
            'accent': '#ef4444',       # أحمر جذاب
            'success': '#10b981',      # أخضر حديث
            'warning': '#f59e0b',      # برتقالي دافئ
            'info': '#06b6d4',         # سماوي
            'light': '#f8fafc',        # رمادي فاتح جداً
            'dark': '#1f2937',         # رمادي داكن
            'white': '#ffffff',        # أبيض نقي
            'gradient_start': '#667eea', # بداية التدرج
            'gradient_end': '#764ba2',   # نهاية التدرج
        }
        
        # تطبيق التصميم العام
        self.setStyleSheet(f"""
            QMainWindow {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.colors['light']}, stop:1 {self.colors['white']});
            }}
            
            QLabel {{
                color: {self.colors['dark']};
                font-family: 'Tahoma', 'Arial';
            }}
            
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.colors['secondary']}, stop:1 {self.colors['primary']});
                color: white;
                border: none;
                border-radius: 12px;
                padding: 12px;
                font-size: 13px;
                font-weight: bold;
                font-family: 'Segoe UI', 'Tahoma', 'Arial';
                min-height: 50px;
            }}
            
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.colors['accent']}, stop:1 {self.colors['warning']});
                transform: translateY(-2px);
                border: 2px solid rgba(255, 255, 255, 0.3);
            }}

            QPushButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.colors['dark']}, stop:1 {self.colors['primary']});
                transform: translateY(1px);
            }}
            
            QFrame {{
                background: transparent;
                border: none;
            }}
        """)
    
    def setup_ui(self):
        """إنشاء واجهة المستخدم"""
        # الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(15)  # مسافة أقل
        main_layout.setContentsMargins(20, 20, 20, 20)  # هوامش أقل
        
        # إنشاء الهيدر
        header_frame = self.create_header()
        main_layout.addWidget(header_frame)
        
        # إنشاء منطقة الأزرار
        buttons_frame = self.create_buttons_area()
        main_layout.addWidget(buttons_frame)
        
        # إنشاء الفوتر
        footer_frame = self.create_footer()
        main_layout.addWidget(footer_frame)
    
    def create_header(self):
        """إنشاء منطقة الهيدر مع الشعار والعنوان"""
        header_frame = QFrame()
        header_frame.setFixedHeight(120)  # ارتفاع أقل
        header_frame.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {self.colors['primary']}, stop:1 {self.colors['secondary']});
                border-radius: 20px;
                border: none;
            }}
        """)
        
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(30, 20, 30, 20)
        
        # منطقة الشعار
        logo_label = QLabel()
        logo_label.setFixedSize(80, 80)  # حجم أصغر
        logo_label.setStyleSheet(f"""
            QLabel {{
                background: {self.colors['white']};
                border-radius: 40px;
                border: 3px solid {self.colors['accent']};
            }}
        """)
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setText("🧁")
        logo_label.setFont(QFont("Arial", 28))  # خط أصغر
        
        # منطقة العنوان
        title_layout = QVBoxLayout()
        
        main_title = QLabel("برنامج محاسبة مصنع الحلويات التقليدية")
        main_title.setFont(QFont("Tahoma", 18, QFont.Bold))  # خط أصغر
        main_title.setStyleSheet(f"color: {self.colors['white']};")
        main_title.setAlignment(Qt.AlignCenter)

        subtitle = QLabel("نظام إدارة متكامل للمحاسبة والإنتاج والمبيعات")
        subtitle.setFont(QFont("Tahoma", 12))  # خط أصغر
        subtitle.setStyleSheet(f"color: {self.colors['light']};")
        subtitle.setAlignment(Qt.AlignCenter)
        
        title_layout.addWidget(main_title)
        title_layout.addWidget(subtitle)
        
        header_layout.addStretch()  # مساحة فارغة في البداية
        header_layout.addWidget(logo_label)
        header_layout.addLayout(title_layout)
        header_layout.addStretch()  # مساحة فارغة في النهاية
        
        return header_frame
    
    def create_buttons_area(self):
        """إنشاء منطقة الأزرار الرئيسية"""
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("QFrame { background: transparent; border: none; }")
        buttons_layout = QGridLayout(buttons_frame)
        buttons_layout.setSpacing(15)  # مسافة أقل بين الأزرار
        buttons_layout.setContentsMargins(15, 15, 15, 15)  # هوامش أقل
        
        # تعريف الأزرار
        buttons_data = [
            ("المشتريات", "🛒", self.open_purchases, 0, 0),
            ("الإنتاج", "🏭", self.open_production, 0, 1),
            ("المبيعات", "💰", self.open_sales, 0, 2),
            ("الطلبيات", "📋", self.open_orders, 0, 3),
            ("المخازن", "📦", self.open_inventory, 1, 0),
            ("العمال", "👥", self.open_workers, 1, 1),
            ("التقارير", "📊", self.open_reports, 1, 2),
            ("الإعدادات", "⚙️", self.open_settings, 1, 3),
        ]
        
        # إنشاء الأزرار
        for text, icon, callback, row, col in buttons_data:
            button = self.create_main_button(text, icon, callback)
            buttons_layout.addWidget(button, row, col)
        
        return buttons_frame

    def create_main_button(self, text, icon, callback):
        """إنشاء زر رئيسي مع تصميم جذاب ومناسب"""
        button = QPushButton()
        button.setFixedSize(200, 100)  # حجم أصغر ومناسب
        button.clicked.connect(callback)

        # تخطيط الزر
        button_layout = QVBoxLayout()
        button_layout.setAlignment(Qt.AlignCenter)

        # أيقونة الزر
        icon_label = QLabel(icon)
        icon_label.setFont(QFont("Arial", 32))
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("color: white; background: transparent;")

        # نص الزر
        text_label = QLabel(text)
        text_label.setFont(QFont("Tahoma", 14, QFont.Bold))
        text_label.setAlignment(Qt.AlignCenter)
        text_label.setStyleSheet("color: white; background: transparent;")

        # إضافة العناصر للتخطيط
        button_layout.addWidget(icon_label)
        button_layout.addWidget(text_label)

        # تطبيق التخطيط على الزر
        button_widget = QWidget()
        button_widget.setLayout(button_layout)

        # تعديل تصميم الزر ليحتوي على الويدجت
        button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.colors['secondary']}, stop:1 {self.colors['primary']});
                color: white;
                border: none;
                border-radius: 15px;
                font-size: 14px;
                font-weight: bold;
                font-family: 'Tahoma', 'Arial';
            }}

            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.colors['accent']}, stop:1 {self.colors['warning']});
                border: 2px solid {self.colors['white']};
            }}

            QPushButton:pressed {{
                background: {self.colors['dark']};
            }}
        """)

        button.setText(f"{icon}\n{text}")

        return button

    def create_footer(self):
        """إنشاء منطقة الفوتر"""
        footer_frame = QFrame()
        footer_frame.setFixedHeight(60)
        footer_frame.setStyleSheet(f"""
            QFrame {{
                background: {self.colors['dark']};
                border-radius: 10px;
                border: none;
            }}
        """)

        footer_layout = QHBoxLayout(footer_frame)
        footer_layout.setContentsMargins(20, 10, 20, 10)

        # معلومات البرنامج
        info_label = QLabel("قلب اللوز لقمان")
        info_label.setFont(QFont("Tahoma", 14, QFont.Bold))  # خط أكبر وأجمل
        info_label.setStyleSheet(f"color: {self.colors['light']};")
        info_label.setAlignment(Qt.AlignCenter)

        footer_layout.addWidget(info_label)

        return footer_frame

    def center_window(self):
        """توسيط النافذة في الشاشة بدقة"""
        # الحصول على معلومات الشاشة
        screen = QApplication.desktop().screenGeometry()

        # الحصول على أبعاد النافذة الحالية
        window_width = self.width()
        window_height = self.height()

        # حساب الموقع المركزي
        x = (screen.width() - window_width) // 2
        y = (screen.height() - window_height) // 2

        # التأكد من أن النافذة لا تخرج عن حدود الشاشة
        x = max(0, min(x, screen.width() - window_width))
        y = max(0, min(y, screen.height() - window_height))

        # تطبيق الموقع الجديد
        self.move(x, y)

        print(f"تم توسيط النافذة في الموقع: ({x}, {y})")
        print(f"أبعاد الشاشة: {screen.width()}x{screen.height()}")
        print(f"أبعاد النافذة: {window_width}x{window_height}")

    # دوال الأزرار
    def open_purchases(self):
        """فتح نافذة المشتريات"""
        try:
            from purchases_window import PurchasesWindow
            self.purchases_window = PurchasesWindow(self)
            self.purchases_window.show()
        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة المشتريات:\n{str(e)}")
            print(f"خطأ في فتح نافذة المشتريات: {e}")
            import traceback
            traceback.print_exc()

    def open_production(self):
        """فتح نافذة الإنتاج"""
        from production_window import ProductionWindow
        self.production_window = ProductionWindow(self)
        self.production_window.show()

    def open_sales(self):
        """فتح نافذة المبيعات"""
        from sales_window import SalesWindow
        self.sales_window = SalesWindow(self)
        self.sales_window.show()

    def open_orders(self):
        """فتح نافذة الطلبيات"""
        from orders_window import OrdersWindow
        self.orders_window = OrdersWindow(self)
        self.orders_window.show()

    def open_inventory(self):
        """فتح نافذة المخازن"""
        from inventory_window import InventoryWindow
        self.inventory_window = InventoryWindow(self)
        self.inventory_window.show()

    def open_workers(self):
        """فتح نافذة العمال"""
        from workers_window import WorkersWindow
        self.workers_window = WorkersWindow(self)
        self.workers_window.show()

    def open_reports(self):
        """فتح نافذة التقارير"""
        from reports_window import ReportsWindow
        self.reports_window = ReportsWindow(self)
        self.reports_window.show()

    def open_settings(self):
        """فتح نافذة الإعدادات"""
        from settings_window import SettingsWindow
        self.settings_window = SettingsWindow(self)
        self.settings_window.show()
