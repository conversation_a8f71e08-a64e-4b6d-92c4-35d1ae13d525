#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشاكل وحدة المشتريات
"""

import sys
import os
sys.path.append('src')

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("🔄 اختبار الاتصال بقاعدة البيانات...")
    
    try:
        from database import db
        
        # اختبار جدول الموردين
        suppliers = db.execute_query("SELECT COUNT(*) as count FROM suppliers")
        print(f"✅ جدول الموردين: {suppliers[0]['count']} سجل")
        
        # اختبار جدول المواد الأولية
        materials = db.execute_query("SELECT COUNT(*) as count FROM raw_materials")
        print(f"✅ جدول المواد الأولية: {materials[0]['count']} سجل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {str(e)}")
        return False

def test_purchases_import():
    """اختبار استيراد وحدة المشتريات"""
    print("🔄 اختبار استيراد وحدة المشتريات...")
    
    try:
        from purchases_window import PurchasesWindow, SupplierDialog, MaterialDialog
        print("✅ تم استيراد جميع الفئات بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استيراد وحدة المشتريات: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_dialog_creation():
    """اختبار إنشاء نوافذ الحوار"""
    print("🔄 اختبار إنشاء نوافذ الحوار...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from purchases_window import SupplierDialog, MaterialDialog
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # اختبار نافذة المورد
        supplier_dialog = SupplierDialog()
        print("✅ تم إنشاء نافذة المورد بنجاح")
        
        # اختبار نافذة المادة
        material_dialog = MaterialDialog()
        print("✅ تم إنشاء نافذة المادة بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء نوافذ الحوار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def add_test_supplier():
    """إضافة مورد تجريبي للاختبار"""
    print("🔄 إضافة مورد تجريبي...")
    
    try:
        from database import db
        
        # إضافة مورد تجريبي
        query = """
            INSERT INTO suppliers (name, phone, address, notes)
            VALUES (?, ?, ?, ?)
        """
        
        supplier_id = db.execute_insert(query, (
            "مورد تجريبي",
            "0501234567", 
            "الرياض - حي النخيل",
            "مورد للاختبار"
        ))
        
        print(f"✅ تم إضافة مورد تجريبي برقم: {supplier_id}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة المورد التجريبي: {str(e)}")
        return False

def add_test_material():
    """إضافة مادة تجريبية للاختبار"""
    print("🔄 إضافة مادة تجريبية...")
    
    try:
        from database import db
        
        # إضافة مادة تجريبية
        query = """
            INSERT INTO raw_materials (name, unit, price, stock_quantity, min_stock, notes)
            VALUES (?, ?, ?, ?, ?, ?)
        """
        
        material_id = db.execute_insert(query, (
            "دقيق تجريبي",
            "كيلوغرام",
            3.50,
            100.0,
            10.0,
            "مادة للاختبار"
        ))
        
        print(f"✅ تم إضافة مادة تجريبية برقم: {material_id}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة المادة التجريبية: {str(e)}")
        return False

def run_full_test():
    """تشغيل اختبار شامل"""
    print("=" * 50)
    print("🔧 إصلاح واختبار وحدة المشتريات")
    print("=" * 50)
    
    tests = [
        ("قاعدة البيانات", test_database_connection),
        ("استيراد الوحدة", test_purchases_import),
        ("نوافذ الحوار", test_dialog_creation),
        ("إضافة مورد تجريبي", add_test_supplier),
        ("إضافة مادة تجريبية", add_test_material),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔄 اختبار {test_name}...")
        if test_func():
            print(f"✅ نجح اختبار {test_name}")
            passed += 1
        else:
            print(f"❌ فشل اختبار {test_name}")
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! وحدة المشتريات تعمل بشكل صحيح")
        print("💡 يمكنك الآن تشغيل البرنامج واختبار إضافة المشتريات")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
    
    print("=" * 50)

if __name__ == "__main__":
    run_full_test()
