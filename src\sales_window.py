#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة المبيعات
إدارة الزبائن وتسجيل المبيعات مع حساب الأسعار والمديونية والصواني
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QGridLayout, QPushButton, QLabel, QFrame, 
                             QTableWidget, QTableWidgetItem, QLineEdit, 
                             QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                             QMessageBox, QDialog, QFormLayout, QDialogButtonBox,
                             QTabWidget, QHeaderView, QDateEdit, QGroupBox,
                             QCheckBox)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont, QIcon
from database import db

class SalesWindow(QMainWindow):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إدارة المبيعات")

        # تحديد أبعاد مناسبة للشاشات المتوسطة
        self.setFixedSize(1200, 650)

        # توسيط النافذة
        self.center_window()
        
        # إعداد التصميم
        self.setup_style()
        
        # إنشاء الواجهة
        self.setup_ui()
        
        # تحديث البيانات
        self.refresh_data()

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        from PyQt5.QtWidgets import QApplication
        screen = QApplication.desktop().screenGeometry()
        window = self.frameGeometry()

        # حساب الموقع المركزي
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2

        # التأكد من أن النافذة لا تخرج عن حدود الشاشة
        x = max(0, min(x, screen.width() - window.width()))
        y = max(0, min(y, screen.height() - window.height()))

        self.move(x, y)
    
    def setup_style(self):
        """إعداد تصميم النافذة"""
        self.colors = {
            'primary': '#2C3E50',
            'secondary': '#3498DB', 
            'accent': '#E74C3C',
            'success': '#27AE60',
            'warning': '#F39C12',
            'light': '#ECF0F1',
            'dark': '#34495E',
            'white': '#FFFFFF'
        }
        
        self.setStyleSheet(f"""
            QMainWindow {{
                background: {self.colors['light']};
            }}
            
            QTabWidget::pane {{
                border: 2px solid {self.colors['primary']};
                border-radius: 10px;
                background: {self.colors['white']};
            }}
            
            QTabBar::tab {{
                background: {self.colors['secondary']};
                color: white;
                padding: 10px 20px;
                margin: 2px;
                border-radius: 5px;
                font-weight: bold;
                font-family: 'Tahoma';
            }}
            
            QTabBar::tab:selected {{
                background: {self.colors['primary']};
            }}
            
            QPushButton {{
                background: {self.colors['secondary']};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
                font-family: 'Tahoma';
                min-height: 35px;
            }}
            
            QPushButton:hover {{
                background: {self.colors['accent']};
            }}
            
            QPushButton:pressed {{
                background: {self.colors['dark']};
            }}
            
            QTableWidget {{
                background: {self.colors['white']};
                border: 2px solid {self.colors['light']};
                border-radius: 8px;
                gridline-color: {self.colors['light']};
                font-family: 'Tahoma';
            }}
            
            QTableWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {self.colors['light']};
            }}
            
            QTableWidget::item:selected {{
                background: {self.colors['secondary']};
                color: white;
            }}
            
            QHeaderView::section {{
                background: {self.colors['primary']};
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
                font-family: 'Tahoma';
            }}
            
            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {{
                border: 2px solid {self.colors['light']};
                border-radius: 5px;
                padding: 8px;
                font-family: 'Tahoma';
                background: {self.colors['white']};
            }}
            
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {{
                border-color: {self.colors['secondary']};
            }}
            
            QGroupBox {{
                font-weight: bold;
                font-family: 'Tahoma';
                border: 2px solid {self.colors['light']};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                color: {self.colors['primary']};
            }}
            
            QCheckBox {{
                font-family: 'Tahoma';
                font-weight: bold;
            }}
        """)
    
    def setup_ui(self):
        """إنشاء واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # إنشاء الهيدر
        header = self.create_header()
        main_layout.addWidget(header)
        
        # إنشاء التبويبات
        tabs = QTabWidget()
        
        # تبويب الزبائن
        customers_tab = self.create_customers_tab()
        tabs.addTab(customers_tab, "الزبائن")
        
        # تبويب فواتير المبيعات
        sales_tab = self.create_sales_tab()
        tabs.addTab(sales_tab, "فواتير المبيعات")
        
        # تبويب المديونية
        debts_tab = self.create_debts_tab()
        tabs.addTab(debts_tab, "المديونية")
        
        main_layout.addWidget(tabs)
        
        # إنشاء أزرار التحكم
        control_buttons = self.create_control_buttons()
        main_layout.addWidget(control_buttons)
    
    def create_header(self):
        """إنشاء منطقة الهيدر"""
        header_frame = QFrame()
        header_frame.setFixedHeight(80)
        header_frame.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {self.colors['primary']}, stop:1 {self.colors['secondary']});
                border-radius: 15px;
            }}
        """)
        
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(20, 10, 20, 10)
        
        # أيقونة ونص الهيدر
        icon_label = QLabel("💰")
        icon_label.setFont(QFont("Arial", 32))
        icon_label.setStyleSheet("color: white; background: transparent;")
        
        title_label = QLabel("إدارة المبيعات")
        title_label.setFont(QFont("Tahoma", 20, QFont.Bold))
        title_label.setStyleSheet("color: white; background: transparent;")
        
        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        return header_frame
    
    def create_customers_tab(self):
        """إنشاء تبويب الزبائن"""
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        add_customer_btn = QPushButton("إضافة زبون جديد")
        add_customer_btn.clicked.connect(self.add_customer)
        
        edit_customer_btn = QPushButton("تعديل زبون")
        edit_customer_btn.clicked.connect(self.edit_customer)
        
        delete_customer_btn = QPushButton("حذف زبون")
        delete_customer_btn.clicked.connect(self.delete_customer)
        delete_customer_btn.setStyleSheet(f"background: {self.colors['accent']};")
        
        view_customer_history_btn = QPushButton("تاريخ الزبون")
        view_customer_history_btn.clicked.connect(self.view_customer_history)
        view_customer_history_btn.setStyleSheet(f"background: {self.colors['success']};")
        
        buttons_layout.addWidget(add_customer_btn)
        buttons_layout.addWidget(edit_customer_btn)
        buttons_layout.addWidget(delete_customer_btn)
        buttons_layout.addWidget(view_customer_history_btn)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        
        # جدول الزبائن
        self.customers_table = QTableWidget()
        self.customers_table.setColumnCount(7)
        self.customers_table.setHorizontalHeaderLabels([
            "الرقم", "اسم الزبون", "رقم الهاتف", "العنوان", "خصم خاص %", "صواني عند الزبون", "ملاحظات"
        ])
        
        # تعديل عرض الأعمدة
        header = self.customers_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.Stretch)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.Stretch)
        
        layout.addWidget(self.customers_table)
        
        return tab_widget

    def create_sales_tab(self):
        """إنشاء تبويب فواتير المبيعات"""
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        add_sale_btn = QPushButton("إضافة فاتورة مبيعات")
        add_sale_btn.clicked.connect(self.add_sale)

        view_sale_btn = QPushButton("عرض الفاتورة")
        view_sale_btn.clicked.connect(self.view_sale)

        print_sale_btn = QPushButton("طباعة الفاتورة")
        print_sale_btn.clicked.connect(self.print_sale)
        print_sale_btn.setStyleSheet(f"background: {self.colors['success']};")

        edit_payment_btn = QPushButton("تعديل الدفعة")
        edit_payment_btn.clicked.connect(self.edit_payment)
        edit_payment_btn.setStyleSheet(f"background: {self.colors['warning']};")

        buttons_layout.addWidget(add_sale_btn)
        buttons_layout.addWidget(view_sale_btn)
        buttons_layout.addWidget(print_sale_btn)
        buttons_layout.addWidget(edit_payment_btn)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        # جدول فواتير المبيعات
        self.sales_table = QTableWidget()
        self.sales_table.setColumnCount(8)
        self.sales_table.setHorizontalHeaderLabels([
            "رقم الفاتورة", "الزبون", "المبلغ الإجمالي", "المبلغ المدفوع", "المتبقي", "صواني مأخوذة", "تاريخ الفاتورة", "ملاحظات"
        ])

        # تعديل عرض الأعمدة
        header = self.sales_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(7, QHeaderView.Stretch)

        layout.addWidget(self.sales_table)

        return tab_widget

    def create_debts_tab(self):
        """إنشاء تبويب المديونية"""
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        refresh_debts_btn = QPushButton("تحديث المديونية")
        refresh_debts_btn.clicked.connect(self.refresh_debts)
        refresh_debts_btn.setStyleSheet(f"background: {self.colors['success']};")

        pay_debt_btn = QPushButton("تسديد دين")
        pay_debt_btn.clicked.connect(self.pay_debt)
        pay_debt_btn.setStyleSheet(f"background: {self.colors['warning']};")

        export_debts_btn = QPushButton("تصدير قائمة الديون")
        export_debts_btn.clicked.connect(self.export_debts)

        buttons_layout.addWidget(refresh_debts_btn)
        buttons_layout.addWidget(pay_debt_btn)
        buttons_layout.addWidget(export_debts_btn)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        # إحصائيات المديونية
        stats_group = QGroupBox("إحصائيات المديونية")
        stats_layout = QHBoxLayout(stats_group)

        self.total_debts_label = QLabel("إجمالي الديون: 0.00 ريال")
        self.total_debts_label.setFont(QFont("Tahoma", 12, QFont.Bold))
        self.total_debts_label.setStyleSheet(f"color: {self.colors['accent']};")

        self.customers_with_debts_label = QLabel("عدد الزبائن المدينين: 0")
        self.customers_with_debts_label.setFont(QFont("Tahoma", 12, QFont.Bold))
        self.customers_with_debts_label.setStyleSheet(f"color: {self.colors['primary']};")

        stats_layout.addWidget(self.total_debts_label)
        stats_layout.addStretch()
        stats_layout.addWidget(self.customers_with_debts_label)

        layout.addWidget(stats_group)

        # جدول المديونية
        self.debts_table = QTableWidget()
        self.debts_table.setColumnCount(6)
        self.debts_table.setHorizontalHeaderLabels([
            "الزبون", "رقم الهاتف", "إجمالي المبيعات", "إجمالي المدفوع", "المتبقي", "آخر فاتورة"
        ])

        # تعديل عرض الأعمدة
        header = self.debts_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)

        layout.addWidget(self.debts_table)

        return tab_widget

    def create_control_buttons(self):
        """إنشاء أزرار التحكم السفلية"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)

        refresh_btn = QPushButton("تحديث البيانات")
        refresh_btn.clicked.connect(self.refresh_data)
        refresh_btn.setStyleSheet(f"background: {self.colors['success']};")

        back_btn = QPushButton("العودة للقائمة الرئيسية")
        back_btn.clicked.connect(self.close)
        back_btn.setStyleSheet(f"background: {self.colors['dark']};")

        buttons_layout.addStretch()
        buttons_layout.addWidget(refresh_btn)
        buttons_layout.addWidget(back_btn)

        return buttons_frame

    def refresh_data(self):
        """تحديث جميع البيانات"""
        self.load_customers()
        self.load_sales()
        self.load_debts()

    def load_customers(self):
        """تحميل بيانات الزبائن"""
        try:
            customers = db.execute_query("SELECT * FROM customers ORDER BY name")

            self.customers_table.setRowCount(len(customers))

            for row, customer in enumerate(customers):
                self.customers_table.setItem(row, 0, QTableWidgetItem(str(customer['id'])))
                self.customers_table.setItem(row, 1, QTableWidgetItem(customer['name'] or ''))
                self.customers_table.setItem(row, 2, QTableWidgetItem(customer['phone'] or ''))
                self.customers_table.setItem(row, 3, QTableWidgetItem(customer['address'] or ''))
                self.customers_table.setItem(row, 4, QTableWidgetItem(f"{customer['special_price_discount']:.1f}%"))
                self.customers_table.setItem(row, 5, QTableWidgetItem(str(customer['trays_with_customer'])))
                self.customers_table.setItem(row, 6, QTableWidgetItem(customer['notes'] or ''))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات الزبائن: {str(e)}")

    def load_sales(self):
        """تحميل بيانات فواتير المبيعات"""
        try:
            query = """
                SELECT si.*, c.name as customer_name
                FROM sales_invoices si
                LEFT JOIN customers c ON si.customer_id = c.id
                ORDER BY si.invoice_date DESC
            """
            sales = db.execute_query(query)

            self.sales_table.setRowCount(len(sales))

            for row, sale in enumerate(sales):
                remaining = sale['total_amount'] - sale['paid_amount']

                self.sales_table.setItem(row, 0, QTableWidgetItem(str(sale['id'])))
                self.sales_table.setItem(row, 1, QTableWidgetItem(sale['customer_name'] or ''))
                self.sales_table.setItem(row, 2, QTableWidgetItem(f"{sale['total_amount']:.2f}"))
                self.sales_table.setItem(row, 3, QTableWidgetItem(f"{sale['paid_amount']:.2f}"))
                self.sales_table.setItem(row, 4, QTableWidgetItem(f"{remaining:.2f}"))
                self.sales_table.setItem(row, 5, QTableWidgetItem(str(sale['trays_taken'])))
                self.sales_table.setItem(row, 6, QTableWidgetItem(sale['invoice_date'] or ''))
                self.sales_table.setItem(row, 7, QTableWidgetItem(sale['notes'] or ''))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات فواتير المبيعات: {str(e)}")

    def load_debts(self):
        """تحميل بيانات المديونية"""
        try:
            query = """
                SELECT
                    c.id,
                    c.name,
                    c.phone,
                    COALESCE(SUM(si.total_amount), 0) as total_sales,
                    COALESCE(SUM(si.paid_amount), 0) as total_paid,
                    COALESCE(SUM(si.total_amount - si.paid_amount), 0) as remaining,
                    MAX(si.invoice_date) as last_invoice_date
                FROM customers c
                LEFT JOIN sales_invoices si ON c.id = si.customer_id
                GROUP BY c.id, c.name, c.phone
                HAVING remaining > 0
                ORDER BY remaining DESC
            """
            debts = db.execute_query(query)

            self.debts_table.setRowCount(len(debts))

            total_debts = 0
            customers_with_debts = 0

            for row, debt in enumerate(debts):
                if debt['remaining'] > 0:
                    customers_with_debts += 1
                    total_debts += debt['remaining']

                    self.debts_table.setItem(row, 0, QTableWidgetItem(debt['name'] or ''))
                    self.debts_table.setItem(row, 1, QTableWidgetItem(debt['phone'] or ''))
                    self.debts_table.setItem(row, 2, QTableWidgetItem(f"{debt['total_sales']:.2f}"))
                    self.debts_table.setItem(row, 3, QTableWidgetItem(f"{debt['total_paid']:.2f}"))
                    self.debts_table.setItem(row, 4, QTableWidgetItem(f"{debt['remaining']:.2f}"))
                    self.debts_table.setItem(row, 5, QTableWidgetItem(debt['last_invoice_date'] or ''))

            # تحديث الإحصائيات
            self.total_debts_label.setText(f"إجمالي الديون: {total_debts:.2f} ريال")
            self.customers_with_debts_label.setText(f"عدد الزبائن المدينين: {customers_with_debts}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات المديونية: {str(e)}")

    # دوال إدارة الزبائن
    def add_customer(self):
        """إضافة زبون جديد"""
        dialog = CustomerDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            try:
                query = """
                    INSERT INTO customers (name, phone, address, special_price_discount, trays_with_customer, notes)
                    VALUES (?, ?, ?, ?, ?, ?)
                """
                db.execute_insert(query, (
                    data['name'], data['phone'], data['address'],
                    data['special_price_discount'], data['trays_with_customer'], data['notes']
                ))
                QMessageBox.information(self, "نجح", "تم إضافة الزبون بنجاح")
                self.load_customers()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في إضافة الزبون: {str(e)}")

    def edit_customer(self):
        """تعديل زبون"""
        current_row = self.customers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار زبون للتعديل")
            return

        customer_id = int(self.customers_table.item(current_row, 0).text())

        # جلب بيانات الزبون الحالية
        customer = db.execute_query("SELECT * FROM customers WHERE id = ?", (customer_id,))
        if not customer:
            QMessageBox.warning(self, "خطأ", "لم يتم العثور على الزبون")
            return

        customer = customer[0]

        dialog = CustomerDialog(self, customer)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            try:
                query = """
                    UPDATE customers
                    SET name = ?, phone = ?, address = ?, special_price_discount = ?,
                        trays_with_customer = ?, notes = ?
                    WHERE id = ?
                """
                db.execute_update(query, (
                    data['name'], data['phone'], data['address'],
                    data['special_price_discount'], data['trays_with_customer'],
                    data['notes'], customer_id
                ))
                QMessageBox.information(self, "نجح", "تم تعديل الزبون بنجاح")
                self.load_customers()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في تعديل الزبون: {str(e)}")

    def delete_customer(self):
        """حذف زبون"""
        current_row = self.customers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار زبون للحذف")
            return

        customer_id = int(self.customers_table.item(current_row, 0).text())
        customer_name = self.customers_table.item(current_row, 1).text()

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف الزبون '{customer_name}'؟\nسيتم حذف جميع فواتير المبيعات المرتبطة به.",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # حذف تفاصيل الفواتير أولاً
                db.execute_update("""
                    DELETE FROM sales_invoice_items
                    WHERE invoice_id IN (SELECT id FROM sales_invoices WHERE customer_id = ?)
                """, (customer_id,))

                # حذف الفواتير
                db.execute_update("DELETE FROM sales_invoices WHERE customer_id = ?", (customer_id,))

                # حذف الزبون
                db.execute_update("DELETE FROM customers WHERE id = ?", (customer_id,))

                QMessageBox.information(self, "نجح", "تم حذف الزبون بنجاح")
                self.load_customers()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في حذف الزبون: {str(e)}")

    def view_customer_history(self):
        """عرض تاريخ الزبون"""
        current_row = self.customers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار زبون لعرض تاريخه")
            return

        QMessageBox.information(self, "قريباً", "سيتم إضافة نافذة عرض تاريخ الزبون قريباً")

    # دوال إدارة المبيعات
    def add_sale(self):
        """إضافة فاتورة مبيعات جديدة"""
        QMessageBox.information(self, "قريباً", "سيتم إضافة نافذة إدخال فواتير المبيعات قريباً")

    def view_sale(self):
        """عرض تفاصيل الفاتورة"""
        current_row = self.sales_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة لعرض التفاصيل")
            return

        QMessageBox.information(self, "قريباً", "سيتم إضافة نافذة عرض تفاصيل الفاتورة قريباً")

    def print_sale(self):
        """طباعة الفاتورة"""
        current_row = self.sales_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة للطباعة")
            return

        QMessageBox.information(self, "قريباً", "سيتم إضافة وظيفة طباعة الفاتورة قريباً")

    def edit_payment(self):
        """تعديل الدفعة"""
        current_row = self.sales_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة لتعديل الدفعة")
            return

        QMessageBox.information(self, "قريباً", "سيتم إضافة نافذة تعديل الدفعة قريباً")

    # دوال إدارة المديونية
    def refresh_debts(self):
        """تحديث المديونية"""
        self.load_debts()
        QMessageBox.information(self, "تم", "تم تحديث بيانات المديونية")

    def pay_debt(self):
        """تسديد دين"""
        current_row = self.debts_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار زبون لتسديد دينه")
            return

        QMessageBox.information(self, "قريباً", "سيتم إضافة نافذة تسديد الدين قريباً")

    def export_debts(self):
        """تصدير قائمة الديون"""
        QMessageBox.information(self, "قريباً", "سيتم إضافة وظيفة تصدير قائمة الديون قريباً")


class CustomerDialog(QDialog):
    """نافذة حوار إضافة/تعديل زبون"""

    def __init__(self, parent=None, customer_data=None):
        super().__init__(parent)
        self.customer_data = customer_data
        self.setWindowTitle("إضافة زبون جديد" if not customer_data else "تعديل زبون")
        self.setFixedSize(500, 500)

        self.setup_ui()

        if customer_data:
            self.load_data()

    def setup_ui(self):
        """إنشاء واجهة النافذة"""
        layout = QVBoxLayout(self)

        # إنشاء النموذج
        form_layout = QFormLayout()

        # حقل اسم الزبون
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("أدخل اسم الزبون")
        form_layout.addRow("اسم الزبون:", self.name_edit)

        # حقل رقم الهاتف
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("أدخل رقم الهاتف")
        form_layout.addRow("رقم الهاتف:", self.phone_edit)

        # حقل العنوان
        self.address_edit = QTextEdit()
        self.address_edit.setPlaceholderText("أدخل العنوان")
        self.address_edit.setMaximumHeight(80)
        form_layout.addRow("العنوان:", self.address_edit)

        # حقل الخصم الخاص
        self.discount_spin = QDoubleSpinBox()
        self.discount_spin.setRange(0, 100)
        self.discount_spin.setDecimals(1)
        self.discount_spin.setSuffix("%")
        self.discount_spin.setToolTip("نسبة الخصم الخاص للزبون")
        form_layout.addRow("خصم خاص:", self.discount_spin)

        # حقل الصواني عند الزبون
        self.trays_spin = QSpinBox()
        self.trays_spin.setRange(0, 9999)
        self.trays_spin.setToolTip("عدد الصواني الموجودة عند الزبون")
        form_layout.addRow("صواني عند الزبون:", self.trays_spin)

        # حقل الملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("أدخل أي ملاحظات إضافية")
        self.notes_edit.setMaximumHeight(100)
        form_layout.addRow("ملاحظات:", self.notes_edit)

        layout.addLayout(form_layout)

        # أزرار التحكم
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)

        # تخصيص النصوص
        buttons.button(QDialogButtonBox.Ok).setText("حفظ")
        buttons.button(QDialogButtonBox.Cancel).setText("إلغاء")

        layout.addWidget(buttons)

    def load_data(self):
        """تحميل البيانات للتعديل"""
        if self.customer_data:
            self.name_edit.setText(self.customer_data['name'] or '')
            self.phone_edit.setText(self.customer_data['phone'] or '')
            self.address_edit.setPlainText(self.customer_data['address'] or '')
            self.discount_spin.setValue(self.customer_data['special_price_discount'] or 0)
            self.trays_spin.setValue(self.customer_data['trays_with_customer'] or 0)
            self.notes_edit.setPlainText(self.customer_data['notes'] or '')

    def get_data(self):
        """الحصول على البيانات المدخلة"""
        return {
            'name': self.name_edit.text().strip(),
            'phone': self.phone_edit.text().strip(),
            'address': self.address_edit.toPlainText().strip(),
            'special_price_discount': self.discount_spin.value(),
            'trays_with_customer': self.trays_spin.value(),
            'notes': self.notes_edit.toPlainText().strip()
        }

    def accept(self):
        """التحقق من صحة البيانات قبل الحفظ"""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم الزبون")
            self.name_edit.setFocus()
            return

        super().accept()
