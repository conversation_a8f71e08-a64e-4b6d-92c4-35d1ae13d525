#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النسخ الاحتياطي والاسترجاع
"""

import sys
import os
import shutil
import sqlite3
from datetime import datetime

# إضافة مجلد src إلى المسار
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from database import db

def test_database_backup():
    """اختبار النسخ الاحتياطي لقاعدة البيانات"""
    print("اختبار النسخ الاحتياطي لقاعدة البيانات...")
    
    try:
        # إنشاء مجلد النسخ الاحتياطية
        backup_folder = "backups"
        os.makedirs(backup_folder, exist_ok=True)
        
        # اسم ملف النسخة الاحتياطية
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f"{backup_folder}/bakery_backup_{timestamp}.db"
        
        # نسخ قاعدة البيانات
        original_db = "bakery_accounting.db"
        if os.path.exists(original_db):
            shutil.copy2(original_db, backup_filename)
            print(f"✅ تم إنشاء نسخة احتياطية: {backup_filename}")
            
            # التحقق من صحة النسخة الاحتياطية
            if verify_backup(backup_filename):
                print("✅ تم التحقق من صحة النسخة الاحتياطية")
                return backup_filename
            else:
                print("❌ النسخة الاحتياطية تالفة")
                return None
        else:
            print("❌ لا توجد قاعدة بيانات أصلية للنسخ")
            return None
            
    except Exception as e:
        print(f"❌ خطأ في النسخ الاحتياطي: {e}")
        return None

def verify_backup(backup_file):
    """التحقق من صحة النسخة الاحتياطية"""
    try:
        # الاتصال بالنسخة الاحتياطية
        conn = sqlite3.connect(backup_file)
        cursor = conn.cursor()
        
        # التحقق من وجود الجداول الأساسية
        required_tables = [
            'suppliers', 'raw_materials', 'recipes', 'customers', 
            'workers', 'products', 'purchase_invoices', 'sales_invoices'
        ]
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        existing_tables = [row[0] for row in cursor.fetchall()]
        
        missing_tables = []
        for table in required_tables:
            if table not in existing_tables:
                missing_tables.append(table)
        
        if missing_tables:
            print(f"❌ جداول مفقودة في النسخة الاحتياطية: {missing_tables}")
            conn.close()
            return False
        
        # التحقق من وجود بيانات
        total_records = 0
        for table in required_tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            total_records += count
            print(f"  - {table}: {count} سجل")
        
        conn.close()
        
        if total_records > 0:
            print(f"✅ النسخة الاحتياطية تحتوي على {total_records} سجل")
            return True
        else:
            print("⚠️ النسخة الاحتياطية فارغة")
            return True  # قد تكون فارغة وهذا طبيعي
            
    except Exception as e:
        print(f"❌ خطأ في التحقق من النسخة الاحتياطية: {e}")
        return False

def test_database_restore(backup_file):
    """اختبار استرجاع قاعدة البيانات"""
    print("اختبار استرجاع قاعدة البيانات...")
    
    if not backup_file or not os.path.exists(backup_file):
        print("❌ لا توجد نسخة احتياطية للاسترجاع")
        return False
    
    try:
        # إنشاء نسخة من قاعدة البيانات الحالية للأمان
        original_db = "bakery_accounting.db"
        temp_backup = f"{original_db}.temp_backup"
        
        if os.path.exists(original_db):
            shutil.copy2(original_db, temp_backup)
            print("✅ تم إنشاء نسخة مؤقتة من قاعدة البيانات الحالية")
        
        # استرجاع النسخة الاحتياطية
        shutil.copy2(backup_file, original_db)
        print("✅ تم استرجاع النسخة الاحتياطية")
        
        # التحقق من صحة الاسترجاع
        if verify_restored_database():
            print("✅ تم التحقق من صحة الاسترجاع")
            
            # استرجاع قاعدة البيانات الأصلية
            if os.path.exists(temp_backup):
                shutil.copy2(temp_backup, original_db)
                os.remove(temp_backup)
                print("✅ تم استرجاع قاعدة البيانات الأصلية")
            
            return True
        else:
            print("❌ فشل في التحقق من صحة الاسترجاع")
            
            # استرجاع قاعدة البيانات الأصلية
            if os.path.exists(temp_backup):
                shutil.copy2(temp_backup, original_db)
                os.remove(temp_backup)
                print("✅ تم استرجاع قاعدة البيانات الأصلية")
            
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاسترجاع: {e}")
        
        # محاولة استرجاع قاعدة البيانات الأصلية
        try:
            if os.path.exists(temp_backup):
                shutil.copy2(temp_backup, original_db)
                os.remove(temp_backup)
                print("✅ تم استرجاع قاعدة البيانات الأصلية")
        except:
            print("❌ فشل في استرجاع قاعدة البيانات الأصلية")
        
        return False

def verify_restored_database():
    """التحقق من صحة قاعدة البيانات المسترجعة"""
    try:
        # إعادة تهيئة الاتصال بقاعدة البيانات
        db.init_database()
        
        # اختبار بعض الاستعلامات
        suppliers = db.execute_query("SELECT COUNT(*) as count FROM suppliers")
        customers = db.execute_query("SELECT COUNT(*) as count FROM customers")
        products = db.execute_query("SELECT COUNT(*) as count FROM products")
        
        print(f"  - الموردين: {suppliers[0]['count'] if suppliers else 0}")
        print(f"  - الزبائن: {customers[0]['count'] if customers else 0}")
        print(f"  - المنتجات: {products[0]['count'] if products else 0}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحقق من قاعدة البيانات المسترجعة: {e}")
        return False

def test_automatic_backup():
    """اختبار النسخ الاحتياطي التلقائي"""
    print("اختبار النسخ الاحتياطي التلقائي...")
    
    try:
        # إنشاء مجلد النسخ التلقائية
        auto_backup_folder = "backups/auto"
        os.makedirs(auto_backup_folder, exist_ok=True)
        
        # محاكاة النسخ التلقائي اليومي
        for i in range(3):
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"{auto_backup_folder}/auto_backup_{timestamp}_{i}.db"
            
            original_db = "bakery_accounting.db"
            if os.path.exists(original_db):
                shutil.copy2(original_db, backup_filename)
                print(f"✅ نسخة تلقائية {i+1}: {backup_filename}")
        
        # التحقق من عدد النسخ التلقائية
        auto_backups = [f for f in os.listdir(auto_backup_folder) if f.endswith('.db')]
        print(f"✅ تم إنشاء {len(auto_backups)} نسخة تلقائية")
        
        # تنظيف النسخ القديمة (محاكاة)
        if len(auto_backups) > 7:  # الاحتفاظ بـ 7 نسخ فقط
            auto_backups.sort()
            for old_backup in auto_backups[:-7]:
                os.remove(os.path.join(auto_backup_folder, old_backup))
                print(f"🗑️ تم حذف النسخة القديمة: {old_backup}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في النسخ التلقائي: {e}")
        return False

def test_backup_compression():
    """اختبار ضغط النسخ الاحتياطية"""
    print("اختبار ضغط النسخ الاحتياطية...")
    
    try:
        import zipfile
        
        # إنشاء مجلد النسخ المضغوطة
        compressed_folder = "backups/compressed"
        os.makedirs(compressed_folder, exist_ok=True)
        
        # ضغط قاعدة البيانات
        original_db = "bakery_accounting.db"
        if os.path.exists(original_db):
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            zip_filename = f"{compressed_folder}/backup_{timestamp}.zip"
            
            with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
                zipf.write(original_db, os.path.basename(original_db))
                
                # إضافة ملفات إضافية إذا وجدت
                if os.path.exists("reports"):
                    for root, dirs, files in os.walk("reports"):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arcname = os.path.relpath(file_path)
                            zipf.write(file_path, arcname)
            
            # مقارنة الأحجام
            original_size = os.path.getsize(original_db)
            compressed_size = os.path.getsize(zip_filename)
            compression_ratio = (1 - compressed_size / original_size) * 100
            
            print(f"✅ تم إنشاء نسخة مضغوطة: {zip_filename}")
            print(f"  - الحجم الأصلي: {original_size:,} بايت")
            print(f"  - الحجم المضغوط: {compressed_size:,} بايت")
            print(f"  - نسبة الضغط: {compression_ratio:.1f}%")
            
            return True
        else:
            print("❌ لا توجد قاعدة بيانات للضغط")
            return False
            
    except ImportError:
        print("❌ مكتبة zipfile غير متوفرة")
        return False
    except Exception as e:
        print(f"❌ خطأ في ضغط النسخة الاحتياطية: {e}")
        return False

def main():
    """الدالة الرئيسية لاختبار النسخ الاحتياطي"""
    print("اختبار النسخ الاحتياطي والاسترجاع")
    print("=" * 50)
    
    # 1. اختبار النسخ الاحتياطي العادي
    print("\n1. اختبار النسخ الاحتياطي العادي:")
    backup_file = test_database_backup()
    
    # 2. اختبار الاسترجاع
    if backup_file:
        print("\n2. اختبار الاسترجاع:")
        test_database_restore(backup_file)
    
    # 3. اختبار النسخ التلقائي
    print("\n3. اختبار النسخ التلقائي:")
    test_automatic_backup()
    
    # 4. اختبار الضغط
    print("\n4. اختبار ضغط النسخ الاحتياطية:")
    test_backup_compression()
    
    print("\n" + "=" * 50)
    print("انتهى اختبار النسخ الاحتياطي والاسترجاع")

if __name__ == "__main__":
    main()
