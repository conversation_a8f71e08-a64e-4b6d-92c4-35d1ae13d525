#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة العمال
إدارة العمال اليوميين والشهريين مع حساب الأجور والسلفات
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QGridLayout, QPushButton, QLabel, QFrame, 
                             QTableWidget, QTableWidgetItem, QLineEdit, 
                             QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                             QMessageBox, QDialog, QFormLayout, QDialogButtonBox,
                             QTabWidget, QHeaderView, QDateEdit, QGroupBox,
                             QCheckBox, QRadioButton, QButtonGroup)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont, QIcon
from database import db

class WorkersWindow(QMainWindow):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إدارة العمال")

        # تحديد أبعاد مناسبة للشاشات المتوسطة
        self.setFixedSize(1200, 650)

        # توسيط النافذة
        self.center_window()
        
        # إعداد التصميم
        self.setup_style()
        
        # إنشاء الواجهة
        self.setup_ui()
        
        # تحديث البيانات
        self.refresh_data()

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        from PyQt5.QtWidgets import QApplication
        screen = QApplication.desktop().screenGeometry()
        window = self.frameGeometry()

        # حساب الموقع المركزي
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2

        # التأكد من أن النافذة لا تخرج عن حدود الشاشة
        x = max(0, min(x, screen.width() - window.width()))
        y = max(0, min(y, screen.height() - window.height()))

        self.move(x, y)
    
    def setup_style(self):
        """إعداد تصميم النافذة"""
        self.colors = {
            'primary': '#2C3E50',
            'secondary': '#3498DB', 
            'accent': '#E74C3C',
            'success': '#27AE60',
            'warning': '#F39C12',
            'light': '#ECF0F1',
            'dark': '#34495E',
            'white': '#FFFFFF'
        }
        
        self.setStyleSheet(f"""
            QMainWindow {{
                background: {self.colors['light']};
            }}
            
            QTabWidget::pane {{
                border: 2px solid {self.colors['primary']};
                border-radius: 10px;
                background: {self.colors['white']};
            }}
            
            QTabBar::tab {{
                background: {self.colors['secondary']};
                color: white;
                padding: 10px 20px;
                margin: 2px;
                border-radius: 5px;
                font-weight: bold;
                font-family: 'Tahoma';
            }}
            
            QTabBar::tab:selected {{
                background: {self.colors['primary']};
            }}
            
            QPushButton {{
                background: {self.colors['secondary']};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
                font-family: 'Tahoma';
                min-height: 35px;
            }}
            
            QPushButton:hover {{
                background: {self.colors['accent']};
            }}
            
            QPushButton:pressed {{
                background: {self.colors['dark']};
            }}
            
            QTableWidget {{
                background: {self.colors['white']};
                border: 2px solid {self.colors['light']};
                border-radius: 8px;
                gridline-color: {self.colors['light']};
                font-family: 'Tahoma';
            }}
            
            QTableWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {self.colors['light']};
            }}
            
            QTableWidget::item:selected {{
                background: {self.colors['secondary']};
                color: white;
            }}
            
            QHeaderView::section {{
                background: {self.colors['primary']};
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
                font-family: 'Tahoma';
            }}
            
            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {{
                border: 2px solid {self.colors['light']};
                border-radius: 5px;
                padding: 8px;
                font-family: 'Tahoma';
                background: {self.colors['white']};
            }}
            
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {{
                border-color: {self.colors['secondary']};
            }}
            
            QGroupBox {{
                font-weight: bold;
                font-family: 'Tahoma';
                border: 2px solid {self.colors['light']};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                color: {self.colors['primary']};
            }}
            
            QRadioButton {{
                font-family: 'Tahoma';
                font-weight: bold;
            }}
        """)
    
    def setup_ui(self):
        """إنشاء واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # إنشاء الهيدر
        header = self.create_header()
        main_layout.addWidget(header)
        
        # إنشاء التبويبات
        tabs = QTabWidget()
        
        # تبويب العمال
        workers_tab = self.create_workers_tab()
        tabs.addTab(workers_tab, "العمال")
        
        # تبويب الحضور
        attendance_tab = self.create_attendance_tab()
        tabs.addTab(attendance_tab, "تسجيل الحضور")
        
        # تبويب السلفات
        advances_tab = self.create_advances_tab()
        tabs.addTab(advances_tab, "السلفات")
        
        # تبويب حساب الأجور
        payroll_tab = self.create_payroll_tab()
        tabs.addTab(payroll_tab, "حساب الأجور")
        
        main_layout.addWidget(tabs)
        
        # إنشاء أزرار التحكم
        control_buttons = self.create_control_buttons()
        main_layout.addWidget(control_buttons)
    
    def create_header(self):
        """إنشاء منطقة الهيدر"""
        header_frame = QFrame()
        header_frame.setFixedHeight(80)
        header_frame.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {self.colors['primary']}, stop:1 {self.colors['secondary']});
                border-radius: 15px;
            }}
        """)
        
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(20, 10, 20, 10)
        
        # أيقونة ونص الهيدر
        icon_label = QLabel("👥")
        icon_label.setFont(QFont("Arial", 32))
        icon_label.setStyleSheet("color: white; background: transparent;")
        
        title_label = QLabel("إدارة العمال")
        title_label.setFont(QFont("Tahoma", 20, QFont.Bold))
        title_label.setStyleSheet("color: white; background: transparent;")
        
        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        return header_frame
    
    def create_workers_tab(self):
        """إنشاء تبويب العمال"""
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        add_worker_btn = QPushButton("إضافة عامل جديد")
        add_worker_btn.clicked.connect(self.add_worker)
        
        edit_worker_btn = QPushButton("تعديل عامل")
        edit_worker_btn.clicked.connect(self.edit_worker)
        
        delete_worker_btn = QPushButton("حذف عامل")
        delete_worker_btn.clicked.connect(self.delete_worker)
        delete_worker_btn.setStyleSheet(f"background: {self.colors['accent']};")
        
        buttons_layout.addWidget(add_worker_btn)
        buttons_layout.addWidget(edit_worker_btn)
        buttons_layout.addWidget(delete_worker_btn)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        
        # جدول العمال
        self.workers_table = QTableWidget()
        self.workers_table.setColumnCount(7)
        self.workers_table.setHorizontalHeaderLabels([
            "الرقم", "اسم العامل", "نوع العمل", "الأجر اليومي", "الراتب الشهري", "رقم الهاتف", "ملاحظات"
        ])
        
        # تعديل عرض الأعمدة
        header = self.workers_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.Stretch)
        
        layout.addWidget(self.workers_table)
        
        return tab_widget
    
    def create_attendance_tab(self):
        """إنشاء تبويب تسجيل الحضور"""
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)
        
        # منطقة تسجيل الحضور
        attendance_group = QGroupBox("تسجيل حضور يومي")
        attendance_layout = QFormLayout(attendance_group)
        
        # اختيار العامل
        self.worker_combo = QComboBox()
        attendance_layout.addRow("العامل:", self.worker_combo)
        
        # تاريخ العمل
        self.work_date = QDateEdit()
        self.work_date.setDate(QDate.currentDate())
        self.work_date.setCalendarPopup(True)
        attendance_layout.addRow("تاريخ العمل:", self.work_date)
        
        # عدد العجنات
        self.batches_spin = QSpinBox()
        self.batches_spin.setRange(0, 999)
        attendance_layout.addRow("عدد العجنات:", self.batches_spin)
        
        # ملاحظات
        self.attendance_notes = QTextEdit()
        self.attendance_notes.setMaximumHeight(80)
        attendance_layout.addRow("ملاحظات:", self.attendance_notes)
        
        # زر التسجيل
        record_attendance_btn = QPushButton("تسجيل الحضور")
        record_attendance_btn.clicked.connect(self.record_attendance)
        record_attendance_btn.setStyleSheet(f"background: {self.colors['success']};")
        attendance_layout.addRow("", record_attendance_btn)
        
        layout.addWidget(attendance_group)
        
        # جدول الحضور
        attendance_table_group = QGroupBox("سجل الحضور")
        attendance_table_layout = QVBoxLayout(attendance_table_group)
        
        self.attendance_table = QTableWidget()
        self.attendance_table.setColumnCount(5)
        self.attendance_table.setHorizontalHeaderLabels([
            "العامل", "تاريخ العمل", "عدد العجنات", "الأجر المستحق", "ملاحظات"
        ])
        
        # تعديل عرض الأعمدة
        header = self.attendance_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.Stretch)
        
        attendance_table_layout.addWidget(self.attendance_table)
        
        layout.addWidget(attendance_table_group)

        return tab_widget

    def create_advances_tab(self):
        """إنشاء تبويب السلفات"""
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)

        # منطقة إضافة سلفة
        advance_group = QGroupBox("إضافة سلفة جديدة")
        advance_layout = QFormLayout(advance_group)

        # اختيار العامل
        self.advance_worker_combo = QComboBox()
        advance_layout.addRow("العامل:", self.advance_worker_combo)

        # مبلغ السلفة
        self.advance_amount = QDoubleSpinBox()
        self.advance_amount.setRange(0, 99999.99)
        self.advance_amount.setDecimals(2)
        self.advance_amount.setSuffix(" ريال")
        advance_layout.addRow("مبلغ السلفة:", self.advance_amount)

        # تاريخ السلفة
        self.advance_date = QDateEdit()
        self.advance_date.setDate(QDate.currentDate())
        self.advance_date.setCalendarPopup(True)
        advance_layout.addRow("تاريخ السلفة:", self.advance_date)

        # ملاحظات السلفة
        self.advance_notes = QTextEdit()
        self.advance_notes.setMaximumHeight(80)
        advance_layout.addRow("ملاحظات:", self.advance_notes)

        # زر إضافة السلفة
        add_advance_btn = QPushButton("إضافة السلفة")
        add_advance_btn.clicked.connect(self.add_advance)
        add_advance_btn.setStyleSheet(f"background: {self.colors['success']};")
        advance_layout.addRow("", add_advance_btn)

        layout.addWidget(advance_group)

        # جدول السلفات
        advances_table_group = QGroupBox("سجل السلفات")
        advances_table_layout = QVBoxLayout(advances_table_group)

        self.advances_table = QTableWidget()
        self.advances_table.setColumnCount(5)
        self.advances_table.setHorizontalHeaderLabels([
            "العامل", "المبلغ", "التاريخ", "ملاحظات", "حذف"
        ])

        # تعديل عرض الأعمدة
        header = self.advances_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.Stretch)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)

        advances_table_layout.addWidget(self.advances_table)

        layout.addWidget(advances_table_group)

        return tab_widget

    def create_payroll_tab(self):
        """إنشاء تبويب حساب الأجور"""
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)

        # منطقة اختيار الفترة
        period_group = QGroupBox("اختيار فترة الحساب")
        period_layout = QHBoxLayout(period_group)

        period_layout.addWidget(QLabel("من تاريخ:"))
        self.payroll_start_date = QDateEdit()
        self.payroll_start_date.setDate(QDate.currentDate().addDays(-30))
        self.payroll_start_date.setCalendarPopup(True)
        period_layout.addWidget(self.payroll_start_date)

        period_layout.addWidget(QLabel("إلى تاريخ:"))
        self.payroll_end_date = QDateEdit()
        self.payroll_end_date.setDate(QDate.currentDate())
        self.payroll_end_date.setCalendarPopup(True)
        period_layout.addWidget(self.payroll_end_date)

        calculate_payroll_btn = QPushButton("حساب الأجور")
        calculate_payroll_btn.clicked.connect(self.calculate_payroll)
        calculate_payroll_btn.setStyleSheet(f"background: {self.colors['success']};")
        period_layout.addWidget(calculate_payroll_btn)

        period_layout.addStretch()

        layout.addWidget(period_group)

        # جدول الأجور
        payroll_group = QGroupBox("تفاصيل الأجور")
        payroll_layout = QVBoxLayout(payroll_group)

        self.payroll_table = QTableWidget()
        self.payroll_table.setColumnCount(7)
        self.payroll_table.setHorizontalHeaderLabels([
            "العامل", "نوع العمل", "العجنات/الأيام", "الأجر الأساسي", "السلفات", "صافي الأجر", "ملاحظات"
        ])

        # تعديل عرض الأعمدة
        header = self.payroll_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.Stretch)

        payroll_layout.addWidget(self.payroll_table)

        layout.addWidget(payroll_group)

        return tab_widget

    def create_control_buttons(self):
        """إنشاء أزرار التحكم السفلية"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)

        refresh_btn = QPushButton("تحديث البيانات")
        refresh_btn.clicked.connect(self.refresh_data)
        refresh_btn.setStyleSheet(f"background: {self.colors['success']};")

        back_btn = QPushButton("العودة للقائمة الرئيسية")
        back_btn.clicked.connect(self.close)
        back_btn.setStyleSheet(f"background: {self.colors['dark']};")

        buttons_layout.addStretch()
        buttons_layout.addWidget(refresh_btn)
        buttons_layout.addWidget(back_btn)

        return buttons_frame

    def refresh_data(self):
        """تحديث جميع البيانات"""
        self.load_workers()
        self.load_workers_combo()
        self.load_attendance()
        self.load_advances()

    def load_workers(self):
        """تحميل بيانات العمال"""
        try:
            workers = db.execute_query("SELECT * FROM workers ORDER BY name")

            self.workers_table.setRowCount(len(workers))

            for row, worker in enumerate(workers):
                self.workers_table.setItem(row, 0, QTableWidgetItem(str(worker['id'])))
                self.workers_table.setItem(row, 1, QTableWidgetItem(worker['name'] or ''))

                worker_type = "يومي" if worker['worker_type'] == 'daily' else "شهري"
                self.workers_table.setItem(row, 2, QTableWidgetItem(worker_type))

                self.workers_table.setItem(row, 3, QTableWidgetItem(f"{worker['daily_rate']:.2f}"))
                self.workers_table.setItem(row, 4, QTableWidgetItem(f"{worker['monthly_salary']:.2f}"))
                self.workers_table.setItem(row, 5, QTableWidgetItem(worker['phone'] or ''))
                self.workers_table.setItem(row, 6, QTableWidgetItem(worker['notes'] or ''))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات العمال: {str(e)}")

    def load_workers_combo(self):
        """تحميل العمال في القوائم المنسدلة"""
        try:
            workers = db.execute_query("SELECT id, name FROM workers ORDER BY name")

            # تحديث قائمة العمال في تبويب الحضور
            self.worker_combo.clear()
            for worker in workers:
                self.worker_combo.addItem(worker['name'], worker['id'])

            # تحديث قائمة العمال في تبويب السلفات
            self.advance_worker_combo.clear()
            for worker in workers:
                self.advance_worker_combo.addItem(worker['name'], worker['id'])

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل قائمة العمال: {str(e)}")

    def load_attendance(self):
        """تحميل بيانات الحضور"""
        try:
            query = """
                SELECT da.*, w.name as worker_name, w.daily_rate
                FROM daily_attendance da
                LEFT JOIN workers w ON da.worker_id = w.id
                ORDER BY da.work_date DESC
                LIMIT 50
            """
            attendance = db.execute_query(query)

            self.attendance_table.setRowCount(len(attendance))

            for row, record in enumerate(attendance):
                wage = record['batches_worked'] * (record['daily_rate'] or 0)

                self.attendance_table.setItem(row, 0, QTableWidgetItem(record['worker_name'] or ''))
                self.attendance_table.setItem(row, 1, QTableWidgetItem(record['work_date'] or ''))
                self.attendance_table.setItem(row, 2, QTableWidgetItem(str(record['batches_worked'])))
                self.attendance_table.setItem(row, 3, QTableWidgetItem(f"{wage:.2f} ريال"))
                self.attendance_table.setItem(row, 4, QTableWidgetItem(record['notes'] or ''))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات الحضور: {str(e)}")

    def load_advances(self):
        """تحميل بيانات السلفات"""
        try:
            query = """
                SELECT wa.*, w.name as worker_name
                FROM worker_advances wa
                LEFT JOIN workers w ON wa.worker_id = w.id
                ORDER BY wa.advance_date DESC
                LIMIT 50
            """
            advances = db.execute_query(query)

            self.advances_table.setRowCount(len(advances))

            for row, advance in enumerate(advances):
                self.advances_table.setItem(row, 0, QTableWidgetItem(advance['worker_name'] or ''))
                self.advances_table.setItem(row, 1, QTableWidgetItem(f"{advance['amount']:.2f} ريال"))
                self.advances_table.setItem(row, 2, QTableWidgetItem(advance['advance_date'] or ''))
                self.advances_table.setItem(row, 3, QTableWidgetItem(advance['notes'] or ''))

                # زر حذف
                delete_btn = QPushButton("حذف")
                delete_btn.setStyleSheet(f"background: {self.colors['accent']};")
                delete_btn.clicked.connect(lambda checked, aid=advance['id']: self.delete_advance(aid))
                self.advances_table.setCellWidget(row, 4, delete_btn)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات السلفات: {str(e)}")

    # دوال إدارة العمال
    def add_worker(self):
        """إضافة عامل جديد"""
        dialog = WorkerDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            try:
                query = """
                    INSERT INTO workers (name, worker_type, daily_rate, monthly_salary, phone, address, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """
                db.execute_insert(query, (
                    data['name'], data['worker_type'], data['daily_rate'],
                    data['monthly_salary'], data['phone'], data['address'], data['notes']
                ))
                QMessageBox.information(self, "نجح", "تم إضافة العامل بنجاح")
                self.refresh_data()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في إضافة العامل: {str(e)}")

    def edit_worker(self):
        """تعديل عامل"""
        current_row = self.workers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عامل للتعديل")
            return

        worker_id = int(self.workers_table.item(current_row, 0).text())

        # جلب بيانات العامل الحالية
        worker = db.execute_query("SELECT * FROM workers WHERE id = ?", (worker_id,))
        if not worker:
            QMessageBox.warning(self, "خطأ", "لم يتم العثور على العامل")
            return

        worker = worker[0]

        dialog = WorkerDialog(self, worker)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            try:
                query = """
                    UPDATE workers
                    SET name = ?, worker_type = ?, daily_rate = ?, monthly_salary = ?,
                        phone = ?, address = ?, notes = ?
                    WHERE id = ?
                """
                db.execute_update(query, (
                    data['name'], data['worker_type'], data['daily_rate'],
                    data['monthly_salary'], data['phone'], data['address'],
                    data['notes'], worker_id
                ))
                QMessageBox.information(self, "نجح", "تم تعديل العامل بنجاح")
                self.refresh_data()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في تعديل العامل: {str(e)}")

    def delete_worker(self):
        """حذف عامل"""
        current_row = self.workers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عامل للحذف")
            return

        worker_id = int(self.workers_table.item(current_row, 0).text())
        worker_name = self.workers_table.item(current_row, 1).text()

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف العامل '{worker_name}'؟\nسيتم حذف جميع سجلات الحضور والسلفات المرتبطة به.",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # حذف السلفات
                db.execute_update("DELETE FROM worker_advances WHERE worker_id = ?", (worker_id,))

                # حذف سجلات الحضور
                db.execute_update("DELETE FROM daily_attendance WHERE worker_id = ?", (worker_id,))

                # حذف العامل
                db.execute_update("DELETE FROM workers WHERE id = ?", (worker_id,))

                QMessageBox.information(self, "نجح", "تم حذف العامل بنجاح")
                self.refresh_data()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في حذف العامل: {str(e)}")

    def record_attendance(self):
        """تسجيل حضور عامل"""
        if self.worker_combo.currentIndex() < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عامل")
            return

        worker_id = self.worker_combo.currentData()
        work_date = self.work_date.date().toPyDate()
        batches = self.batches_spin.value()
        notes = self.attendance_notes.toPlainText().strip()

        if batches <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال عدد العجنات")
            return

        try:
            # التحقق من عدم وجود تسجيل مسبق لنفس العامل في نفس التاريخ
            existing = db.execute_query(
                "SELECT id FROM daily_attendance WHERE worker_id = ? AND work_date = ?",
                (worker_id, work_date)
            )

            if existing:
                QMessageBox.warning(self, "تحذير", "تم تسجيل حضور هذا العامل مسبقاً في هذا التاريخ")
                return

            query = """
                INSERT INTO daily_attendance (worker_id, work_date, batches_worked, notes)
                VALUES (?, ?, ?, ?)
            """
            db.execute_insert(query, (worker_id, work_date, batches, notes))

            QMessageBox.information(self, "نجح", "تم تسجيل الحضور بنجاح")

            # مسح الحقول
            self.batches_spin.setValue(0)
            self.attendance_notes.clear()

            self.load_attendance()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تسجيل الحضور: {str(e)}")

    def add_advance(self):
        """إضافة سلفة"""
        if self.advance_worker_combo.currentIndex() < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عامل")
            return

        worker_id = self.advance_worker_combo.currentData()
        amount = self.advance_amount.value()
        advance_date = self.advance_date.date().toPyDate()
        notes = self.advance_notes.toPlainText().strip()

        if amount <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ السلفة")
            return

        try:
            query = """
                INSERT INTO worker_advances (worker_id, amount, advance_date, notes)
                VALUES (?, ?, ?, ?)
            """
            db.execute_insert(query, (worker_id, amount, advance_date, notes))

            QMessageBox.information(self, "نجح", "تم إضافة السلفة بنجاح")

            # مسح الحقول
            self.advance_amount.setValue(0)
            self.advance_notes.clear()

            self.load_advances()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة السلفة: {str(e)}")

    def delete_advance(self, advance_id):
        """حذف سلفة"""
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            "هل أنت متأكد من حذف هذه السلفة؟",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                db.execute_update("DELETE FROM worker_advances WHERE id = ?", (advance_id,))
                QMessageBox.information(self, "نجح", "تم حذف السلفة بنجاح")
                self.load_advances()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في حذف السلفة: {str(e)}")

    def calculate_payroll(self):
        """حساب الأجور للفترة المحددة"""
        start_date = self.payroll_start_date.date().toPyDate()
        end_date = self.payroll_end_date.date().toPyDate()

        if start_date > end_date:
            QMessageBox.warning(self, "خطأ", "تاريخ البداية يجب أن يكون قبل تاريخ النهاية")
            return

        try:
            # جلب جميع العمال
            workers = db.execute_query("SELECT * FROM workers ORDER BY name")

            self.payroll_table.setRowCount(len(workers))

            for row, worker in enumerate(workers):
                worker_id = worker['id']
                worker_name = worker['name']
                worker_type = worker['worker_type']

                if worker_type == 'daily':
                    # حساب أجر العامل اليومي
                    attendance_query = """
                        SELECT SUM(batches_worked) as total_batches
                        FROM daily_attendance
                        WHERE worker_id = ? AND work_date BETWEEN ? AND ?
                    """
                    attendance_result = db.execute_query(attendance_query, (worker_id, start_date, end_date))
                    total_batches = attendance_result[0]['total_batches'] or 0

                    basic_wage = total_batches * (worker['daily_rate'] or 0)
                    work_info = f"{total_batches} عجنة"

                else:  # monthly
                    # حساب أجر العامل الشهري (نسبة من الراتب حسب الأيام)
                    from datetime import datetime
                    days_in_period = (end_date - start_date).days + 1
                    days_in_month = 30  # افتراض 30 يوم في الشهر

                    basic_wage = (worker['monthly_salary'] or 0) * (days_in_period / days_in_month)
                    work_info = f"{days_in_period} يوم"

                # حساب السلفات
                advances_query = """
                    SELECT SUM(amount) as total_advances
                    FROM worker_advances
                    WHERE worker_id = ? AND advance_date BETWEEN ? AND ?
                """
                advances_result = db.execute_query(advances_query, (worker_id, start_date, end_date))
                total_advances = advances_result[0]['total_advances'] or 0

                # صافي الأجر
                net_wage = basic_wage - total_advances

                # ملء الجدول
                self.payroll_table.setItem(row, 0, QTableWidgetItem(worker_name))
                self.payroll_table.setItem(row, 1, QTableWidgetItem("يومي" if worker_type == 'daily' else "شهري"))
                self.payroll_table.setItem(row, 2, QTableWidgetItem(work_info))
                self.payroll_table.setItem(row, 3, QTableWidgetItem(f"{basic_wage:.2f} ريال"))
                self.payroll_table.setItem(row, 4, QTableWidgetItem(f"{total_advances:.2f} ريال"))
                self.payroll_table.setItem(row, 5, QTableWidgetItem(f"{net_wage:.2f} ريال"))
                self.payroll_table.setItem(row, 6, QTableWidgetItem(""))

                # تلوين الصفوف حسب صافي الأجر
                if net_wage < 0:
                    for col in range(7):
                        item = self.payroll_table.item(row, col)
                        if item:
                            item.setBackground(Qt.red)
                            item.setForeground(Qt.white)
                elif net_wage > 0:
                    for col in range(7):
                        item = self.payroll_table.item(row, col)
                        if item:
                            item.setBackground(Qt.green)
                            item.setForeground(Qt.white)

            QMessageBox.information(self, "تم", "تم حساب الأجور بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حساب الأجور: {str(e)}")


class WorkerDialog(QDialog):
    """نافذة حوار إضافة/تعديل عامل"""

    def __init__(self, parent=None, worker_data=None):
        super().__init__(parent)
        self.worker_data = worker_data
        self.setWindowTitle("إضافة عامل جديد" if not worker_data else "تعديل عامل")
        self.setFixedSize(500, 600)

        self.setup_ui()

        if worker_data:
            self.load_data()

    def setup_ui(self):
        """إنشاء واجهة النافذة"""
        layout = QVBoxLayout(self)

        # إنشاء النموذج
        form_layout = QFormLayout()

        # حقل اسم العامل
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("أدخل اسم العامل")
        form_layout.addRow("اسم العامل:", self.name_edit)

        # نوع العمل
        worker_type_group = QGroupBox("نوع العمل")
        worker_type_layout = QVBoxLayout(worker_type_group)

        self.worker_type_group = QButtonGroup()

        self.daily_radio = QRadioButton("عامل يومي")
        self.daily_radio.setChecked(True)
        self.daily_radio.toggled.connect(self.on_worker_type_changed)
        self.worker_type_group.addButton(self.daily_radio, 0)
        worker_type_layout.addWidget(self.daily_radio)

        self.monthly_radio = QRadioButton("عامل شهري")
        self.monthly_radio.toggled.connect(self.on_worker_type_changed)
        self.worker_type_group.addButton(self.monthly_radio, 1)
        worker_type_layout.addWidget(self.monthly_radio)

        form_layout.addRow(worker_type_group)

        # حقل الأجر اليومي
        self.daily_rate_spin = QDoubleSpinBox()
        self.daily_rate_spin.setRange(0, 9999.99)
        self.daily_rate_spin.setDecimals(2)
        self.daily_rate_spin.setSuffix(" ريال/عجنة")
        form_layout.addRow("الأجر اليومي:", self.daily_rate_spin)

        # حقل الراتب الشهري
        self.monthly_salary_spin = QDoubleSpinBox()
        self.monthly_salary_spin.setRange(0, 99999.99)
        self.monthly_salary_spin.setDecimals(2)
        self.monthly_salary_spin.setSuffix(" ريال/شهر")
        self.monthly_salary_spin.setEnabled(False)
        form_layout.addRow("الراتب الشهري:", self.monthly_salary_spin)

        # حقل رقم الهاتف
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("أدخل رقم الهاتف")
        form_layout.addRow("رقم الهاتف:", self.phone_edit)

        # حقل العنوان
        self.address_edit = QTextEdit()
        self.address_edit.setPlaceholderText("أدخل العنوان")
        self.address_edit.setMaximumHeight(80)
        form_layout.addRow("العنوان:", self.address_edit)

        # حقل الملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("أدخل أي ملاحظات إضافية")
        self.notes_edit.setMaximumHeight(100)
        form_layout.addRow("ملاحظات:", self.notes_edit)

        layout.addLayout(form_layout)

        # أزرار التحكم
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)

        # تخصيص النصوص
        buttons.button(QDialogButtonBox.Ok).setText("حفظ")
        buttons.button(QDialogButtonBox.Cancel).setText("إلغاء")

        layout.addWidget(buttons)

    def on_worker_type_changed(self):
        """تغيير نوع العامل"""
        if self.daily_radio.isChecked():
            self.daily_rate_spin.setEnabled(True)
            self.monthly_salary_spin.setEnabled(False)
            self.monthly_salary_spin.setValue(0)
        else:
            self.daily_rate_spin.setEnabled(False)
            self.daily_rate_spin.setValue(0)
            self.monthly_salary_spin.setEnabled(True)

    def load_data(self):
        """تحميل البيانات للتعديل"""
        if self.worker_data:
            self.name_edit.setText(self.worker_data['name'] or '')
            self.phone_edit.setText(self.worker_data['phone'] or '')
            self.address_edit.setPlainText(self.worker_data['address'] or '')
            self.notes_edit.setPlainText(self.worker_data['notes'] or '')

            if self.worker_data['worker_type'] == 'daily':
                self.daily_radio.setChecked(True)
                self.daily_rate_spin.setValue(self.worker_data['daily_rate'] or 0)
            else:
                self.monthly_radio.setChecked(True)
                self.monthly_salary_spin.setValue(self.worker_data['monthly_salary'] or 0)

    def get_data(self):
        """الحصول على البيانات المدخلة"""
        worker_type = 'daily' if self.daily_radio.isChecked() else 'monthly'

        return {
            'name': self.name_edit.text().strip(),
            'worker_type': worker_type,
            'daily_rate': self.daily_rate_spin.value() if worker_type == 'daily' else 0,
            'monthly_salary': self.monthly_salary_spin.value() if worker_type == 'monthly' else 0,
            'phone': self.phone_edit.text().strip(),
            'address': self.address_edit.toPlainText().strip(),
            'notes': self.notes_edit.toPlainText().strip()
        }

    def accept(self):
        """التحقق من صحة البيانات قبل الحفظ"""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم العامل")
            self.name_edit.setFocus()
            return

        if self.daily_radio.isChecked() and self.daily_rate_spin.value() <= 0:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال الأجر اليومي للعامل")
            self.daily_rate_spin.setFocus()
            return

        if self.monthly_radio.isChecked() and self.monthly_salary_spin.value() <= 0:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال الراتب الشهري للعامل")
            self.monthly_salary_spin.setFocus()
            return

        super().accept()
