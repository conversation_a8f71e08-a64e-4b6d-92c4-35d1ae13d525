# دليل المستخدم - برنامج محاسبة مصنع الحلويات التقليدية

## مقدمة
مرحباً بك في برنامج محاسبة مصنع الحلويات التقليدية، نظام إدارة متكامل مصمم خصيصاً لإدارة مصانع الحلويات التقليدية.

## متطلبات النظام
- **نظام التشغيل:** Windows 7 أو أحدث
- **ذاكرة الوصول العشوائي:** 2 جيجابايت على الأقل
- **مساحة القرص الصلب:** 100 ميجابايت
- **Python:** 3.8 أو أحدث (للتشغيل من الكود المصدري)

## التثبيت والتشغيل

### الطريقة الأولى: تشغيل ملف exe (الأسهل)
1. انقر نقراً مزدوجاً على ملف `برنامج_محاسبة_الحلويات.exe`
2. انتظر حتى يتم تحميل البرنامج
3. ابدأ باستخدام البرنامج

### الطريقة الثانية: تشغيل من الكود المصدري
1. تأكد من تثبيت Python 3.8 أو أحدث
2. انقر نقراً مزدوجاً على ملف `run.bat`
3. أو افتح موجه الأوامر وشغل: `python main.py`

## الواجهة الرئيسية
عند تشغيل البرنامج، ستظهر الواجهة الرئيسية التي تحتوي على:

### الأزرار الرئيسية:
- **🛒 المشتريات:** إدارة الموردين والمواد الأولية وفواتير المشتريات
- **🏭 الإنتاج:** إدارة الوصفات والمنتجات وتسجيل الإنتاج اليومي
- **💰 المبيعات:** إدارة الزبائن وفواتير المبيعات والمديونية
- **📋 الطلبيات:** إدارة الطلبيات وحساب المواد المطلوبة
- **📦 المخازن:** تتبع المواد والمنتجات وأدوات التغليف
- **👥 العمال:** إدارة العمال والأجور والسلفات
- **📊 التقارير:** إنشاء التقارير المالية والإحصائية
- **⚙️ الإعدادات:** النسخ الاحتياطي وإعدادات البرنامج

## الوحدات الرئيسية

### 1. وحدة المشتريات
**الوظائف المتاحة:**
- إضافة وتعديل وحذف الموردين
- إدارة المواد الأولية مع تحديد الأسعار والوحدات
- تسجيل فواتير المشتريات
- تتبع المدفوعات للموردين

**كيفية الاستخدام:**
1. ابدأ بإضافة الموردين من تبويب "الموردين"
2. أضف المواد الأولية من تبويب "المواد الأولية"
3. سجل فواتير المشتريات من تبويب "فواتير المشتريات"

### 2. وحدة الإنتاج
**الوظائف المتاحة:**
- إنشاء وصفات الحلويات مع تحديد المكونات
- إدارة المنتجات وربطها بالوصفات
- تسجيل الإنتاج اليومي
- حساب المواد المطلوبة تلقائياً

**كيفية الاستخدام:**
1. أنشئ الوصفات وحدد مكوناتها
2. أضف المنتجات واربطها بالوصفات
3. سجل الإنتاج اليومي مع عدد العجنات

### 3. وحدة المبيعات
**الوظائف المتاحة:**
- إدارة الزبائن مع الأسعار الخاصة
- تسجيل فواتير المبيعات
- تتبع المديونية والدفعات
- إدارة الصواني المأخوذة والمرتجعة

**كيفية الاستخدام:**
1. أضف الزبائن مع تحديد الخصومات الخاصة
2. سجل فواتير المبيعات
3. تابع المديونية من تبويب "المديونية"

### 4. وحدة العمال
**الوظائف المتاحة:**
- إدارة العمال اليوميين والشهريين
- تسجيل الحضور اليومي
- إدارة السلفات
- حساب الأجور تلقائياً

**كيفية الاستخدام:**
1. أضف العمال وحدد نوع العمل (يومي/شهري)
2. سجل الحضور اليومي للعمال
3. أضف السلفات عند الحاجة
4. احسب الأجور للفترة المطلوبة

### 5. وحدة المخازن
**الوظائف المتاحة:**
- تتبع المواد الأولية والمنتجات
- تنبيهات المواد المنخفضة والمنتهية
- إدارة أدوات التغليف
- تقارير المخزون

### 6. وحدة التقارير
**التقارير المتاحة:**
- تقارير المبيعات (ملخص وتفصيلي)
- تقارير المشتريات
- تقارير المخزون
- تقارير المديونية
- تقارير الأرباح والخسائر

### 7. وحدة الإعدادات
**الوظائف المتاحة:**
- إنشاء نسخة احتياطية
- استرجاع نسخة احتياطية
- إعادة ضبط البرنامج
- فحص سلامة البيانات

## النصائح والإرشادات

### نصائح الاستخدام:
1. **ابدأ بالبيانات الأساسية:** أضف الموردين والزبائن والمواد أولاً
2. **أنشئ نسخة احتياطية دورية:** من وحدة الإعدادات
3. **راجع التنبيهات:** تحقق من تنبيهات المخزون بانتظام
4. **استخدم التقارير:** لمتابعة الأداء المالي

### حل المشاكل الشائعة:

**مشكلة: البرنامج لا يفتح**
- تأكد من تثبيت Python بشكل صحيح
- تحقق من تثبيت جميع المتطلبات
- جرب تشغيل `run.bat` كمدير

**مشكلة: خطأ في قاعدة البيانات**
- استخدم "فحص سلامة البيانات" من الإعدادات
- استرجع نسخة احتياطية سابقة إذا لزم الأمر

**مشكلة: البرنامج بطيء**
- أغلق البرامج الأخرى غير الضرورية
- تأكد من توفر مساحة كافية على القرص الصلب

## الدعم الفني
للحصول على المساعدة:
1. راجع هذا الدليل أولاً
2. تحقق من ملف `README.md`
3. استخدم وظيفة "فحص سلامة البيانات"

## معلومات الإصدار
- **الإصدار:** 1.0
- **تاريخ الإصدار:** 2025-06-18
- **المطور:** Augment Agent
- **اللغة:** العربية مع دعم RTL

---
**ملاحظة:** هذا البرنامج مطور خصيصاً لمصانع الحلويات التقليدية ويدعم جميع العمليات المحاسبية والإدارية المطلوبة.
