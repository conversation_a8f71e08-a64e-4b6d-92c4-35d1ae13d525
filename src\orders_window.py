#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة الطلبيات
إدارة الطلبيات مع حساب العجنات والمواد المطلوبة تلقائياً
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QGridLayout, QPushButton, QLabel, QFrame, 
                             QTableWidget, QTableWidgetItem, QLineEdit, 
                             QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                             QMessageBox, QDialog, QFormLayout, QDialogButtonBox,
                             QTabWidget, QHeaderView, QDateEdit, QGroupBox,
                             QCheckBox, QSplitter)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont, QIcon
from database import db

class OrdersWindow(QMainWindow):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إدارة الطلبيات")

        # تحديد أبعاد مناسبة للشاشات المتوسطة
        self.setFixedSize(1200, 650)

        # توسيط النافذة
        self.center_window()
        
        # إعداد التصميم
        self.setup_style()
        
        # إنشاء الواجهة
        self.setup_ui()
        
        # تحديث البيانات
        self.refresh_data()

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        from PyQt5.QtWidgets import QApplication
        screen = QApplication.desktop().screenGeometry()
        window = self.frameGeometry()

        # حساب الموقع المركزي
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2

        # التأكد من أن النافذة لا تخرج عن حدود الشاشة
        x = max(0, min(x, screen.width() - window.width()))
        y = max(0, min(y, screen.height() - window.height()))

        self.move(x, y)
    
    def setup_style(self):
        """إعداد تصميم النافذة"""
        self.colors = {
            'primary': '#2C3E50',
            'secondary': '#3498DB', 
            'accent': '#E74C3C',
            'success': '#27AE60',
            'warning': '#F39C12',
            'light': '#ECF0F1',
            'dark': '#34495E',
            'white': '#FFFFFF'
        }
        
        self.setStyleSheet(f"""
            QMainWindow {{
                background: {self.colors['light']};
            }}
            
            QTabWidget::pane {{
                border: 2px solid {self.colors['primary']};
                border-radius: 10px;
                background: {self.colors['white']};
            }}
            
            QTabBar::tab {{
                background: {self.colors['secondary']};
                color: white;
                padding: 10px 20px;
                margin: 2px;
                border-radius: 5px;
                font-weight: bold;
                font-family: 'Tahoma';
            }}
            
            QTabBar::tab:selected {{
                background: {self.colors['primary']};
            }}
            
            QPushButton {{
                background: {self.colors['secondary']};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
                font-family: 'Tahoma';
                min-height: 35px;
            }}
            
            QPushButton:hover {{
                background: {self.colors['accent']};
            }}
            
            QPushButton:pressed {{
                background: {self.colors['dark']};
            }}
            
            QTableWidget {{
                background: {self.colors['white']};
                border: 2px solid {self.colors['light']};
                border-radius: 8px;
                gridline-color: {self.colors['light']};
                font-family: 'Tahoma';
            }}
            
            QTableWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {self.colors['light']};
            }}
            
            QTableWidget::item:selected {{
                background: {self.colors['secondary']};
                color: white;
            }}
            
            QHeaderView::section {{
                background: {self.colors['primary']};
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
                font-family: 'Tahoma';
            }}
            
            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {{
                border: 2px solid {self.colors['light']};
                border-radius: 5px;
                padding: 8px;
                font-family: 'Tahoma';
                background: {self.colors['white']};
            }}
            
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {{
                border-color: {self.colors['secondary']};
            }}
            
            QGroupBox {{
                font-weight: bold;
                font-family: 'Tahoma';
                border: 2px solid {self.colors['light']};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                color: {self.colors['primary']};
            }}
        """)
    
    def setup_ui(self):
        """إنشاء واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # إنشاء الهيدر
        header = self.create_header()
        main_layout.addWidget(header)
        
        # إنشاء التبويبات
        tabs = QTabWidget()
        
        # تبويب الطلبيات
        orders_tab = self.create_orders_tab()
        tabs.addTab(orders_tab, "الطلبيات")
        
        # تبويب حساب المواد
        materials_calc_tab = self.create_materials_calc_tab()
        tabs.addTab(materials_calc_tab, "حساب المواد المطلوبة")
        
        main_layout.addWidget(tabs)
        
        # إنشاء أزرار التحكم
        control_buttons = self.create_control_buttons()
        main_layout.addWidget(control_buttons)
    
    def create_header(self):
        """إنشاء منطقة الهيدر"""
        header_frame = QFrame()
        header_frame.setFixedHeight(80)
        header_frame.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {self.colors['primary']}, stop:1 {self.colors['secondary']});
                border-radius: 15px;
            }}
        """)
        
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(20, 10, 20, 10)
        
        # أيقونة ونص الهيدر
        icon_label = QLabel("📋")
        icon_label.setFont(QFont("Arial", 32))
        icon_label.setStyleSheet("color: white; background: transparent;")
        
        title_label = QLabel("إدارة الطلبيات")
        title_label.setFont(QFont("Tahoma", 20, QFont.Bold))
        title_label.setStyleSheet("color: white; background: transparent;")
        
        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        return header_frame
    
    def create_orders_tab(self):
        """إنشاء تبويب الطلبيات"""
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        add_order_btn = QPushButton("إضافة طلبية جديدة")
        add_order_btn.clicked.connect(self.add_order)
        
        edit_order_btn = QPushButton("تعديل طلبية")
        edit_order_btn.clicked.connect(self.edit_order)
        
        delete_order_btn = QPushButton("حذف طلبية")
        delete_order_btn.clicked.connect(self.delete_order)
        delete_order_btn.setStyleSheet(f"background: {self.colors['accent']};")
        
        complete_order_btn = QPushButton("إكمال طلبية")
        complete_order_btn.clicked.connect(self.complete_order)
        complete_order_btn.setStyleSheet(f"background: {self.colors['success']};")
        
        buttons_layout.addWidget(add_order_btn)
        buttons_layout.addWidget(edit_order_btn)
        buttons_layout.addWidget(delete_order_btn)
        buttons_layout.addWidget(complete_order_btn)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        
        # جدول الطلبيات
        self.orders_table = QTableWidget()
        self.orders_table.setColumnCount(7)
        self.orders_table.setHorizontalHeaderLabels([
            "رقم الطلبية", "الزبون", "تاريخ الطلبية", "تاريخ التسليم", "الحالة", "المبلغ الإجمالي", "ملاحظات"
        ])
        
        # تعديل عرض الأعمدة
        header = self.orders_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.Stretch)
        
        layout.addWidget(self.orders_table)
        
        return tab_widget
    
    def create_materials_calc_tab(self):
        """إنشاء تبويب حساب المواد المطلوبة"""
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)
        
        # منطقة اختيار الطلبية
        order_selection_group = QGroupBox("اختيار الطلبية")
        order_selection_layout = QHBoxLayout(order_selection_group)
        
        order_selection_layout.addWidget(QLabel("الطلبية:"))
        self.order_combo = QComboBox()
        order_selection_layout.addWidget(self.order_combo)
        
        calculate_btn = QPushButton("حساب المواد المطلوبة")
        calculate_btn.clicked.connect(self.calculate_materials)
        calculate_btn.setStyleSheet(f"background: {self.colors['success']};")
        order_selection_layout.addWidget(calculate_btn)
        
        order_selection_layout.addStretch()
        
        layout.addWidget(order_selection_group)
        
        # جدول المواد المطلوبة
        materials_group = QGroupBox("المواد المطلوبة")
        materials_layout = QVBoxLayout(materials_group)
        
        self.materials_table = QTableWidget()
        self.materials_table.setColumnCount(5)
        self.materials_table.setHorizontalHeaderLabels([
            "المادة", "الكمية المطلوبة", "الوحدة", "المتوفر في المخزن", "النقص"
        ])
        
        # تعديل عرض الأعمدة
        header = self.materials_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        
        materials_layout.addWidget(self.materials_table)
        
        layout.addWidget(materials_group)
        
        return tab_widget
    
    def create_control_buttons(self):
        """إنشاء أزرار التحكم السفلية"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        
        refresh_btn = QPushButton("تحديث البيانات")
        refresh_btn.clicked.connect(self.refresh_data)
        refresh_btn.setStyleSheet(f"background: {self.colors['success']};")
        
        back_btn = QPushButton("العودة للقائمة الرئيسية")
        back_btn.clicked.connect(self.close)
        back_btn.setStyleSheet(f"background: {self.colors['dark']};")
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(refresh_btn)
        buttons_layout.addWidget(back_btn)
        
        return buttons_frame
    
    def refresh_data(self):
        """تحديث جميع البيانات"""
        self.load_orders()
        self.load_orders_combo()
    
    def load_orders(self):
        """تحميل بيانات الطلبيات"""
        try:
            query = """
                SELECT o.*, c.name as customer_name 
                FROM orders o
                LEFT JOIN customers c ON o.customer_id = c.id
                ORDER BY o.order_date DESC
            """
            orders = db.execute_query(query)
            
            self.orders_table.setRowCount(len(orders))
            
            for row, order in enumerate(orders):
                self.orders_table.setItem(row, 0, QTableWidgetItem(str(order['id'])))
                self.orders_table.setItem(row, 1, QTableWidgetItem(order['customer_name'] or ''))
                self.orders_table.setItem(row, 2, QTableWidgetItem(order['order_date'] or ''))
                self.orders_table.setItem(row, 3, QTableWidgetItem(order['delivery_date'] or ''))
                self.orders_table.setItem(row, 4, QTableWidgetItem(order['status'] or ''))
                self.orders_table.setItem(row, 5, QTableWidgetItem(f"{order['total_amount']:.2f}"))
                self.orders_table.setItem(row, 6, QTableWidgetItem(order['notes'] or ''))
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات الطلبيات: {str(e)}")
    
    def load_orders_combo(self):
        """تحميل الطلبيات في القائمة المنسدلة"""
        try:
            self.order_combo.clear()
            
            query = """
                SELECT o.id, c.name as customer_name, o.order_date
                FROM orders o
                LEFT JOIN customers c ON o.customer_id = c.id
                WHERE o.status = 'pending'
                ORDER BY o.order_date DESC
            """
            orders = db.execute_query(query)
            
            for order in orders:
                text = f"طلبية #{order['id']} - {order['customer_name']} - {order['order_date']}"
                self.order_combo.addItem(text, order['id'])
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل قائمة الطلبيات: {str(e)}")
    
    # دوال إدارة الطلبيات
    def add_order(self):
        """إضافة طلبية جديدة"""
        QMessageBox.information(self, "قريباً", "سيتم إضافة نافذة إنشاء الطلبيات قريباً")
    
    def edit_order(self):
        """تعديل طلبية"""
        current_row = self.orders_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار طلبية للتعديل")
            return
        
        QMessageBox.information(self, "قريباً", "سيتم إضافة نافذة تعديل الطلبيات قريباً")
    
    def delete_order(self):
        """حذف طلبية"""
        current_row = self.orders_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار طلبية للحذف")
            return
        
        order_id = int(self.orders_table.item(current_row, 0).text())
        
        reply = QMessageBox.question(
            self, "تأكيد الحذف", 
            f"هل أنت متأكد من حذف الطلبية رقم {order_id}؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # حذف تفاصيل الطلبية
                db.execute_update("DELETE FROM order_items WHERE order_id = ?", (order_id,))
                
                # حذف الطلبية
                db.execute_update("DELETE FROM orders WHERE id = ?", (order_id,))
                
                QMessageBox.information(self, "تم", "تم حذف الطلبية بنجاح")
                self.refresh_data()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في حذف الطلبية: {str(e)}")
    
    def complete_order(self):
        """إكمال طلبية"""
        current_row = self.orders_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار طلبية لإكمالها")
            return
        
        order_id = int(self.orders_table.item(current_row, 0).text())
        
        try:
            db.execute_update("UPDATE orders SET status = 'completed' WHERE id = ?", (order_id,))
            QMessageBox.information(self, "تم", "تم إكمال الطلبية بنجاح")
            self.refresh_data()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إكمال الطلبية: {str(e)}")
    
    def calculate_materials(self):
        """حساب المواد المطلوبة للطلبية"""
        if self.order_combo.currentIndex() < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار طلبية")
            return
        
        order_id = self.order_combo.currentData()
        
        try:
            # جلب تفاصيل الطلبية
            query = """
                SELECT oi.*, p.name as product_name, p.recipe_id, p.weight
                FROM order_items oi
                LEFT JOIN products p ON oi.product_id = p.id
                WHERE oi.order_id = ?
            """
            order_items = db.execute_query(query, (order_id,))
            
            # حساب المواد المطلوبة
            materials_needed = {}
            
            for item in order_items:
                if item['recipe_id']:
                    # جلب مكونات الوصفة
                    ingredients_query = """
                        SELECT ri.*, rm.name as material_name, rm.unit, rm.stock_quantity
                        FROM recipe_ingredients ri
                        LEFT JOIN raw_materials rm ON ri.material_id = rm.id
                        WHERE ri.recipe_id = ?
                    """
                    ingredients = db.execute_query(ingredients_query, (item['recipe_id'],))
                    
                    # حساب عدد العجنات المطلوبة
                    total_weight_needed = item['quantity'] * item['weight']
                    recipe = db.execute_query("SELECT batch_weight FROM recipes WHERE id = ?", (item['recipe_id'],))[0]
                    batches_needed = total_weight_needed / recipe['batch_weight']
                    
                    # حساب كمية كل مادة
                    for ingredient in ingredients:
                        material_name = ingredient['material_name']
                        quantity_needed = ingredient['quantity'] * batches_needed
                        
                        if material_name in materials_needed:
                            materials_needed[material_name]['quantity'] += quantity_needed
                        else:
                            materials_needed[material_name] = {
                                'quantity': quantity_needed,
                                'unit': ingredient['unit'],
                                'stock': ingredient['stock_quantity']
                            }
            
            # عرض النتائج في الجدول
            self.materials_table.setRowCount(len(materials_needed))
            
            row = 0
            for material_name, data in materials_needed.items():
                shortage = max(0, data['quantity'] - data['stock'])
                
                self.materials_table.setItem(row, 0, QTableWidgetItem(material_name))
                self.materials_table.setItem(row, 1, QTableWidgetItem(f"{data['quantity']:.2f}"))
                self.materials_table.setItem(row, 2, QTableWidgetItem(data['unit']))
                self.materials_table.setItem(row, 3, QTableWidgetItem(f"{data['stock']:.2f}"))
                self.materials_table.setItem(row, 4, QTableWidgetItem(f"{shortage:.2f}"))
                
                # تلوين الصفوف حسب النقص
                if shortage > 0:
                    for col in range(5):
                        item = self.materials_table.item(row, col)
                        if item:
                            item.setBackground(Qt.red)
                            item.setForeground(Qt.white)
                
                row += 1
            
            QMessageBox.information(self, "تم", "تم حساب المواد المطلوبة بنجاح")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حساب المواد المطلوبة: {str(e)}")
