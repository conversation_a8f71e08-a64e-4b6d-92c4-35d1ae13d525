#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة بيانات تجريبية للاختبار
"""

import sys
import os
sys.path.append('src')

from database import db
from datetime import datetime, date

def add_sample_data():
    """إضافة بيانات تجريبية شاملة"""
    
    print("🔄 إضافة بيانات تجريبية...")
    
    try:
        # إضافة موردين
        print("📦 إضافة الموردين...")
        suppliers_data = [
            ("شركة الأغذية المتحدة", "0501234567", "الرياض - حي الملز", "مورد رئيسي للمواد الأولية"),
            ("مؤسسة النخيل التجارية", "0509876543", "جدة - حي الروضة", "متخصص في العسل والتمور"),
            ("شركة الخليج للتوريدات", "0551122334", "الدمام - حي الفيصلية", "مورد المكسرات والفواكه المجففة"),
        ]
        
        for supplier in suppliers_data:
            db.execute_insert(
                "INSERT INTO suppliers (name, phone, address, notes) VALUES (?, ?, ?, ?)",
                supplier
            )
        
        # إضافة مواد أولية
        print("🥄 إضافة المواد الأولية...")
        materials_data = [
            ("دقيق أبيض", "كيلوغرام", 3.50, 500.0, 50.0, "دقيق فاخر للحلويات"),
            ("سكر أبيض", "كيلوغرام", 2.80, 300.0, 30.0, "سكر ناعم"),
            ("زبدة طبيعية", "كيلوغرام", 25.00, 50.0, 10.0, "زبدة بقري طازجة"),
            ("بيض طازج", "كرتونة", 12.00, 20.0, 5.0, "بيض أحمر طازج"),
            ("عسل طبيعي", "كيلوغرام", 45.00, 100.0, 10.0, "عسل سدر أصلي"),
            ("لوز مقشر", "كيلوغرام", 35.00, 25.0, 5.0, "لوز كاليفورنيا"),
            ("جوز هند مبشور", "كيلوغرام", 18.00, 30.0, 5.0, "جوز هند طازج"),
            ("تمر مجدول", "كيلوغرام", 28.00, 40.0, 8.0, "تمر مجدول فاخر"),
            ("ماء ورد", "لتر", 15.00, 10.0, 2.0, "ماء ورد طبيعي"),
            ("هيل مطحون", "كيلوغرام", 120.00, 5.0, 1.0, "هيل أخضر مطحون"),
        ]
        
        for material in materials_data:
            db.execute_insert(
                "INSERT INTO raw_materials (name, unit, price, stock_quantity, min_stock, notes) VALUES (?, ?, ?, ?, ?, ?)",
                material
            )
        
        # إضافة وصفات
        print("📋 إضافة الوصفات...")
        recipes_data = [
            ("معمول التمر التقليدي", 10.0, "وصفة تقليدية لمعمول التمر بالسمن البلدي"),
            ("بقلاوة بالفستق", 8.0, "بقلاوة محشوة بالفستق الحلبي"),
            ("كنافة نابلسية", 12.0, "كنافة طرية بالجبن والقشطة"),
            ("غريبة باللوز", 6.0, "غريبة هشة محشوة باللوز المطحون"),
        ]
        
        recipe_ids = []
        for recipe in recipes_data:
            recipe_id = db.execute_insert(
                "INSERT INTO recipes (name, batch_weight, description) VALUES (?, ?, ?)",
                recipe
            )
            recipe_ids.append(recipe_id)
        
        # إضافة مكونات الوصفات
        print("🧾 إضافة مكونات الوصفات...")
        # مكونات معمول التمر (recipe_id = 1)
        ingredients_data = [
            (recipe_ids[0], 1, 3.0),  # دقيق
            (recipe_ids[0], 2, 0.5),  # سكر
            (recipe_ids[0], 3, 1.0),  # زبدة
            (recipe_ids[0], 8, 2.0),  # تمر
            (recipe_ids[0], 9, 0.1),  # ماء ورد
            
            # مكونات بقلاوة (recipe_id = 2)
            (recipe_ids[1], 1, 2.0),  # دقيق
            (recipe_ids[1], 3, 1.5),  # زبدة
            (recipe_ids[1], 6, 1.0),  # لوز
            (recipe_ids[1], 5, 0.5),  # عسل
            
            # مكونات كنافة (recipe_id = 3)
            (recipe_ids[2], 1, 4.0),  # دقيق
            (recipe_ids[2], 2, 1.0),  # سكر
            (recipe_ids[2], 3, 2.0),  # زبدة
            (recipe_ids[2], 5, 1.0),  # عسل
            
            # مكونات غريبة (recipe_id = 4)
            (recipe_ids[3], 1, 2.5),  # دقيق
            (recipe_ids[3], 2, 0.8),  # سكر
            (recipe_ids[3], 3, 1.2),  # زبدة
            (recipe_ids[3], 6, 0.5),  # لوز
        ]
        
        for ingredient in ingredients_data:
            db.execute_insert(
                "INSERT INTO recipe_ingredients (recipe_id, material_id, quantity) VALUES (?, ?, ?)",
                ingredient
            )
        
        # إضافة منتجات
        print("🍰 إضافة المنتجات...")
        products_data = [
            ("معمول تمر - صغير", recipe_ids[0], 0.5, "علبة كرتون", 0, 15.00, 100.0),
            ("معمول تمر - كبير", recipe_ids[0], 1.0, "علبة كرتون", 0, 25.00, 50.0),
            ("بقلاوة فستق - صينية", recipe_ids[1], 2.0, "صينية ألمنيوم", 1, 45.00, 30.0),
            ("كنافة نابلسية - صينية", recipe_ids[2], 3.0, "صينية ألمنيوم", 1, 60.00, 20.0),
            ("غريبة لوز - كيس", recipe_ids[3], 0.5, "كيس بلاستيك", 0, 18.00, 80.0),
        ]
        
        for product in products_data:
            db.execute_insert(
                "INSERT INTO products (name, recipe_id, weight, packaging_type, is_returnable, price, stock_quantity) VALUES (?, ?, ?, ?, ?, ?, ?)",
                product
            )
        
        # إضافة زبائن
        print("👥 إضافة الزبائن...")
        customers_data = [
            ("محمد أحمد العلي", "0501111111", "الرياض - حي النخيل", 5.0, 10, "زبون مميز - خصم خاص"),
            ("فاطمة سالم", "0502222222", "جدة - حي الزهراء", 0.0, 5, "زبونة دائمة"),
            ("عبدالله محمد", "0503333333", "الدمام - حي الشاطئ", 10.0, 15, "تاجر جملة"),
            ("نورا خالد", "0504444444", "الرياض - حي الملك فهد", 0.0, 3, "طلبات مناسبات"),
            ("سعد الغامدي", "0505555555", "أبها - حي المنهل", 7.5, 8, "زبون موسمي"),
        ]
        
        for customer in customers_data:
            db.execute_insert(
                "INSERT INTO customers (name, phone, address, special_price_discount, trays_with_customer, notes) VALUES (?, ?, ?, ?, ?, ?)",
                customer
            )
        
        # إضافة عمال
        print("👷 إضافة العمال...")
        workers_data = [
            ("أحمد محمد الخباز", "daily", 25.0, 0.0, "0506666666", "الرياض", "خباز رئيسي"),
            ("علي سالم المساعد", "daily", 20.0, 0.0, "0507777777", "الرياض", "مساعد خباز"),
            ("فهد عبدالله المشرف", "monthly", 0.0, 4500.0, "0508888888", "الرياض", "مشرف الإنتاج"),
            ("خالد أحمد العامل", "daily", 18.0, 0.0, "0509999999", "الرياض", "عامل تغليف"),
        ]
        
        for worker in workers_data:
            db.execute_insert(
                "INSERT INTO workers (name, worker_type, daily_rate, monthly_salary, phone, address, notes) VALUES (?, ?, ?, ?, ?, ?, ?)",
                worker
            )
        
        # إضافة فواتير مشتريات
        print("🧾 إضافة فواتير المشتريات...")
        today = date.today()
        
        purchase_invoices_data = [
            (1, "INV-001", 1750.0, 1750.0, today, "فاتورة دقيق وسكر"),
            (2, "INV-002", 4500.0, 3000.0, today, "فاتورة عسل وتمور"),
            (3, "INV-003", 875.0, 875.0, today, "فاتورة مكسرات"),
        ]
        
        for invoice in purchase_invoices_data:
            db.execute_insert(
                "INSERT INTO purchase_invoices (supplier_id, invoice_number, total_amount, paid_amount, invoice_date, notes) VALUES (?, ?, ?, ?, ?, ?)",
                invoice
            )
        
        # إضافة فواتير مبيعات
        print("💰 إضافة فواتير المبيعات...")
        sales_invoices_data = [
            (1, "SALE-001", 150.0, 150.0, 2, today, "طلبية معمول"),
            (2, "SALE-002", 90.0, 50.0, 1, today, "طلبية غريبة"),
            (3, "SALE-003", 270.0, 270.0, 3, today, "طلبية بقلاوة"),
            (4, "SALE-004", 120.0, 80.0, 0, today, "طلبية مناسبة"),
        ]
        
        for invoice in sales_invoices_data:
            db.execute_insert(
                "INSERT INTO sales_invoices (customer_id, invoice_number, total_amount, paid_amount, trays_taken, invoice_date, notes) VALUES (?, ?, ?, ?, ?, ?, ?)",
                invoice
            )
        
        # إضافة حضور عمال
        print("📅 إضافة سجلات الحضور...")
        attendance_data = [
            (1, today, 8, "عمل عادي"),
            (2, today, 6, "عمل نصف يوم"),
            (4, today, 10, "عمل إضافي"),
        ]
        
        for attendance in attendance_data:
            db.execute_insert(
                "INSERT INTO daily_attendance (worker_id, work_date, batches_worked, notes) VALUES (?, ?, ?, ?)",
                attendance
            )
        
        # إضافة سلفات
        print("💳 إضافة السلفات...")
        advances_data = [
            (1, 200.0, today, "سلفة شخصية"),
            (2, 150.0, today, "سلفة طارئة"),
        ]
        
        for advance in advances_data:
            db.execute_insert(
                "INSERT INTO worker_advances (worker_id, amount, advance_date, notes) VALUES (?, ?, ?, ?)",
                advance
            )
        
        # إضافة أدوات تغليف
        print("📦 إضافة أدوات التغليف...")
        packaging_data = [
            ("صواني ألمنيوم كبيرة", 100, 25, 75, 1, "صواني قابلة للإرجاع"),
            ("صواني ألمنيوم صغيرة", 150, 40, 110, 1, "صواني صغيرة"),
            ("أكياس بلاستيك شفافة", 500, 0, 500, 0, "أكياس غير قابلة للإرجاع"),
            ("علب كرتون مزخرفة", 200, 0, 200, 0, "علب هدايا"),
        ]
        
        for tool in packaging_data:
            db.execute_insert(
                "INSERT INTO packaging_tools (name, total_quantity, with_customers, available_quantity, is_returnable, notes) VALUES (?, ?, ?, ?, ?, ?)",
                tool
            )
        
        print("✅ تم إضافة جميع البيانات التجريبية بنجاح!")
        print("\n📊 ملخص البيانات المضافة:")
        print(f"   • {len(suppliers_data)} موردين")
        print(f"   • {len(materials_data)} مواد أولية")
        print(f"   • {len(recipes_data)} وصفات")
        print(f"   • {len(products_data)} منتجات")
        print(f"   • {len(customers_data)} زبائن")
        print(f"   • {len(workers_data)} عمال")
        print(f"   • {len(purchase_invoices_data)} فواتير مشتريات")
        print(f"   • {len(sales_invoices_data)} فواتير مبيعات")
        print(f"   • {len(packaging_data)} أدوات تغليف")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة البيانات التجريبية: {str(e)}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("🏭 برنامج محاسبة مصنع الحلويات التقليدية")
    print("📊 إضافة بيانات تجريبية للاختبار")
    print("=" * 50)
    
    if add_sample_data():
        print("\n🎉 تم إعداد البيانات التجريبية بنجاح!")
        print("يمكنك الآن تشغيل البرنامج واختبار جميع الوظائف.")
    else:
        print("\n❌ فشل في إعداد البيانات التجريبية.")
