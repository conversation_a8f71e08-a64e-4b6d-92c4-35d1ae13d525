#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حذف جميع البيانات التجريبية من قاعدة البيانات
إعادة تعيين البرنامج لحالة نظيفة
"""

import sys
import os
sys.path.append('src')

from database import db
from PyQt5.QtWidgets import QApplication, QMessageBox

def clear_all_data():
    """حذف جميع البيانات من قاعدة البيانات"""
    
    print("🗑️ بدء عملية حذف البيانات التجريبية...")
    
    try:
        # قائمة الجداول التي تحتوي على بيانات
        tables_to_clear = [
            'daily_production',
            'sales_invoice_items', 
            'purchase_invoice_items',
            'recipe_ingredients',
            'worker_advances',
            'daily_attendance',
            'sales_invoices',
            'purchase_invoices',
            'packaging_tools',
            'products',
            'recipes',
            'customers',
            'workers',
            'raw_materials',
            'suppliers'
        ]
        
        # حذف البيانات من كل جدول
        for table in tables_to_clear:
            try:
                # حذف جميع البيانات
                db.execute_query(f"DELETE FROM {table}")
                
                # إعادة تعيين العداد التلقائي
                db.execute_query(f"DELETE FROM sqlite_sequence WHERE name='{table}'")
                
                print(f"   ✅ تم حذف بيانات جدول {table}")
                
            except Exception as e:
                print(f"   ⚠️ تحذير في جدول {table}: {str(e)}")
        
        print("✅ تم حذف جميع البيانات التجريبية بنجاح!")
        
        # عرض إحصائيات بعد الحذف
        print("\n📊 إحصائيات قاعدة البيانات بعد الحذف:")
        
        for table in tables_to_clear:
            try:
                count = db.execute_query(f"SELECT COUNT(*) as count FROM {table}")[0]['count']
                print(f"   • {table}: {count} سجل")
            except:
                pass
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في حذف البيانات: {str(e)}")
        return False

def confirm_deletion():
    """تأكيد عملية الحذف من المستخدم"""
    
    app = QApplication(sys.argv)
    
    # رسالة تأكيد
    reply = QMessageBox.question(
        None,
        "تأكيد حذف البيانات",
        "هل أنت متأكد من حذف جميع البيانات التجريبية؟\n\n"
        "تحذير: هذه العملية لا يمكن التراجع عنها!\n"
        "سيتم حذف:\n"
        "• جميع الموردين والزبائن\n"
        "• جميع المواد والمنتجات\n"
        "• جميع الفواتير والطلبيات\n"
        "• جميع بيانات العمال\n"
        "• جميع سجلات الإنتاج\n\n"
        "هل تريد المتابعة؟",
        QMessageBox.Yes | QMessageBox.No,
        QMessageBox.No
    )
    
    return reply == QMessageBox.Yes

def create_fresh_database():
    """إنشاء قاعدة بيانات نظيفة"""
    
    print("🔄 إنشاء قاعدة بيانات نظيفة...")
    
    try:
        # التأكد من وجود مجلد قاعدة البيانات
        os.makedirs("database", exist_ok=True)
        
        # إنشاء الجداول (ستكون فارغة)
        db.create_tables()
        
        print("✅ تم إنشاء قاعدة بيانات نظيفة بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {str(e)}")
        return False

def backup_before_clear():
    """إنشاء نسخة احتياطية قبل الحذف"""
    
    print("💾 إنشاء نسخة احتياطية قبل الحذف...")
    
    try:
        from datetime import datetime
        import shutil
        
        # إنشاء مجلد النسخ الاحتياطية
        os.makedirs("backup", exist_ok=True)
        
        # اسم النسخة الاحتياطية
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"backup_before_clear_{timestamp}.db"
        backup_path = os.path.join("backup", backup_filename)
        
        # نسخ قاعدة البيانات
        if os.path.exists("database/sweets_factory.db"):
            shutil.copy2("database/sweets_factory.db", backup_path)
            print(f"✅ تم إنشاء نسخة احتياطية: {backup_filename}")
            return True
        else:
            print("⚠️ لا توجد قاعدة بيانات للنسخ الاحتياطي")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("=" * 60)
    print("🗑️ أداة حذف البيانات التجريبية")
    print("🏭 برنامج محاسبة مصنع الحلويات التقليدية")
    print("=" * 60)
    
    # تأكيد من المستخدم
    if not confirm_deletion():
        print("❌ تم إلغاء عملية الحذف بواسطة المستخدم")
        return
    
    # إنشاء نسخة احتياطية
    if not backup_before_clear():
        print("❌ فشل في إنشاء النسخة الاحتياطية")
        return
    
    # حذف البيانات
    if clear_all_data():
        print("\n🎉 تم حذف جميع البيانات التجريبية بنجاح!")
        print("\n📝 ملاحظات:")
        print("   • تم الاحتفاظ بهيكل قاعدة البيانات")
        print("   • تم إنشاء نسخة احتياطية قبل الحذف")
        print("   • البرنامج جاهز للاستخدام مع بيانات حقيقية")
        print("   • يمكنك الآن بدء إدخال بياناتك الفعلية")
        
    else:
        print("\n❌ فشل في حذف البيانات")

if __name__ == "__main__":
    main()
