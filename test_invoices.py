#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وظائف الفواتير في وحدة المشتريات
"""

import sys
import os
sys.path.append('src')

def test_invoice_functions():
    """اختبار وظائف الفواتير"""
    print("🧪 اختبار وظائف الفواتير...")
    
    try:
        from database import db
        
        # التحقق من وجود موردين
        suppliers = db.execute_query("SELECT COUNT(*) as count FROM suppliers")[0]['count']
        print(f"✅ عدد الموردين المتوفرين: {suppliers}")
        
        if suppliers == 0:
            print("⚠️ لا يوجد موردين. سأضيف مورد تجريبي...")
            
            # إضافة مورد تجريبي
            supplier_id = db.execute_insert("""
                INSERT INTO suppliers (name, phone, address, notes)
                VALUES (?, ?, ?, ?)
            """, ("مورد تجريبي للفواتير", "0501234567", "الرياض", "مورد للاختبار"))
            
            print(f"✅ تم إضافة مورد تجريبي برقم: {supplier_id}")
        
        # اختبار إضافة فاتورة
        print("🔄 اختبار إضافة فاتورة...")
        
        from datetime import date
        
        invoice_id = db.execute_insert("""
            INSERT INTO purchase_invoices (supplier_id, invoice_number, total_amount, paid_amount, invoice_date, notes)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (1, "INV-TEST-001", 1500.00, 1000.00, date.today(), "فاتورة اختبار"))
        
        print(f"✅ تم إضافة فاتورة تجريبية برقم: {invoice_id}")
        
        # التحقق من الفاتورة
        invoice = db.execute_query("SELECT * FROM purchase_invoices WHERE id = ?", (invoice_id,))
        if invoice:
            print(f"✅ تم التحقق من الفاتورة: {invoice[0]['invoice_number']}")
        
        # اختبار استعلام الفواتير مع الموردين
        print("🔄 اختبار استعلام الفواتير...")
        
        invoices = db.execute_query("""
            SELECT pi.*, s.name as supplier_name
            FROM purchase_invoices pi
            LEFT JOIN suppliers s ON pi.supplier_id = s.id
            ORDER BY pi.invoice_date DESC
        """)
        
        print(f"✅ تم جلب {len(invoices)} فاتورة")
        
        for invoice in invoices:
            print(f"   📄 فاتورة: {invoice['invoice_number']} - مورد: {invoice['supplier_name']}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الفواتير: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_invoice_ui():
    """اختبار واجهة الفواتير"""
    print("🖥️ اختبار واجهة الفواتير...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from purchases_window import PurchasesWindow
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء نافذة المشتريات
        window = PurchasesWindow()
        window.show()
        
        print("✅ تم فتح نافذة المشتريات بنجاح")
        print("📝 يمكنك الآن:")
        print("   1. الانتقال لتبويب 'فواتير المشتريات'")
        print("   2. اضغط 'إضافة فاتورة جديدة'")
        print("   3. اختبر 'عرض الفاتورة' و 'طباعة الفاتورة'")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة الفواتير: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🧪 اختبار وظائف الفواتير")
    print("=" * 50)
    
    # اختبار قاعدة البيانات أولاً
    if test_invoice_functions():
        print("\n✅ اختبار قاعدة البيانات نجح")
        
        # اختبار الواجهة
        print("\n🖥️ سيتم فتح واجهة الاختبار...")
        test_invoice_ui()
    else:
        print("\n❌ فشل اختبار قاعدة البيانات")

if __name__ == "__main__":
    main()
