#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة التقارير
إنشاء وتصدير التقارير المالية بصيغة PDF
"""

import os
from datetime import datetime, date
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QGridLayout, QPushButton, QLabel, QFrame, 
                             QTableWidget, QTableWidgetItem, QLineEdit, 
                             QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                             QMessageBox, QDialog, QFormLayout, QDialogButtonBox,
                             QTabWidget, QHeaderView, QDateEdit, QGroupBox,
                             QCheckBox, QProgressBar, QFileDialog)
from PyQt5.QtCore import Qt, QDate, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QIcon
from database import db

class ReportsWindow(QMainWindow):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("التقارير المالية")

        # تحديد أبعاد مناسبة للشاشات المتوسطة
        self.setFixedSize(1100, 600)

        # توسيط النافذة
        self.center_window()
        
        # إعداد التصميم
        self.setup_style()
        
        # إنشاء الواجهة
        self.setup_ui()

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        from PyQt5.QtWidgets import QApplication
        screen = QApplication.desktop().screenGeometry()
        window = self.frameGeometry()

        # حساب الموقع المركزي
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2

        # التأكد من أن النافذة لا تخرج عن حدود الشاشة
        x = max(0, min(x, screen.width() - window.width()))
        y = max(0, min(y, screen.height() - window.height()))

        self.move(x, y)
    
    def setup_style(self):
        """إعداد تصميم النافذة"""
        self.colors = {
            'primary': '#2C3E50',
            'secondary': '#3498DB', 
            'accent': '#E74C3C',
            'success': '#27AE60',
            'warning': '#F39C12',
            'light': '#ECF0F1',
            'dark': '#34495E',
            'white': '#FFFFFF'
        }
        
        self.setStyleSheet(f"""
            QMainWindow {{
                background: {self.colors['light']};
            }}
            
            QPushButton {{
                background: {self.colors['secondary']};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 15px 25px;
                font-weight: bold;
                font-family: 'Tahoma';
                min-height: 40px;
                font-size: 14px;
            }}
            
            QPushButton:hover {{
                background: {self.colors['accent']};
            }}
            
            QPushButton:pressed {{
                background: {self.colors['dark']};
            }}
            
            QGroupBox {{
                font-weight: bold;
                font-family: 'Tahoma';
                border: 2px solid {self.colors['light']};
                border-radius: 8px;
                margin-top: 15px;
                padding-top: 15px;
                font-size: 14px;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: {self.colors['primary']};
                font-size: 16px;
            }}
            
            QDateEdit {{
                border: 2px solid {self.colors['light']};
                border-radius: 5px;
                padding: 8px;
                font-family: 'Tahoma';
                background: {self.colors['white']};
                font-size: 12px;
            }}
            
            QDateEdit:focus {{
                border-color: {self.colors['secondary']};
            }}
            
            QLabel {{
                font-family: 'Tahoma';
                font-size: 12px;
                color: {self.colors['dark']};
            }}
            
            QProgressBar {{
                border: 2px solid {self.colors['light']};
                border-radius: 5px;
                text-align: center;
                font-family: 'Tahoma';
                font-weight: bold;
            }}
            
            QProgressBar::chunk {{
                background-color: {self.colors['success']};
                border-radius: 3px;
            }}
        """)
    
    def setup_ui(self):
        """إنشاء واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)
        
        # إنشاء الهيدر
        header = self.create_header()
        main_layout.addWidget(header)
        
        # إنشاء منطقة التقارير
        reports_area = self.create_reports_area()
        main_layout.addWidget(reports_area)
        
        # إنشاء شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # إنشاء أزرار التحكم
        control_buttons = self.create_control_buttons()
        main_layout.addWidget(control_buttons)
    
    def create_header(self):
        """إنشاء منطقة الهيدر"""
        header_frame = QFrame()
        header_frame.setFixedHeight(100)
        header_frame.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {self.colors['primary']}, stop:1 {self.colors['secondary']});
                border-radius: 15px;
            }}
        """)
        
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(30, 15, 30, 15)
        
        # أيقونة ونص الهيدر
        icon_label = QLabel("📊")
        icon_label.setFont(QFont("Arial", 40))
        icon_label.setStyleSheet("color: white; background: transparent;")
        
        title_label = QLabel("التقارير المالية")
        title_label.setFont(QFont("Tahoma", 24, QFont.Bold))
        title_label.setStyleSheet("color: white; background: transparent;")
        
        subtitle_label = QLabel("إنشاء وتصدير التقارير المالية والإحصائية")
        subtitle_label.setFont(QFont("Tahoma", 14))
        subtitle_label.setStyleSheet("color: #ECF0F1; background: transparent;")
        
        text_layout = QVBoxLayout()
        text_layout.addWidget(title_label)
        text_layout.addWidget(subtitle_label)
        
        header_layout.addWidget(icon_label)
        header_layout.addLayout(text_layout)
        header_layout.addStretch()
        
        return header_frame
    
    def create_reports_area(self):
        """إنشاء منطقة التقارير"""
        reports_frame = QFrame()
        reports_layout = QVBoxLayout(reports_frame)
        
        # منطقة اختيار الفترة الزمنية
        date_group = QGroupBox("اختيار الفترة الزمنية")
        date_layout = QHBoxLayout(date_group)
        
        # تاريخ البداية
        date_layout.addWidget(QLabel("من تاريخ:"))
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setCalendarPopup(True)
        date_layout.addWidget(self.start_date)
        
        # تاريخ النهاية
        date_layout.addWidget(QLabel("إلى تاريخ:"))
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setCalendarPopup(True)
        date_layout.addWidget(self.end_date)
        
        date_layout.addStretch()
        
        reports_layout.addWidget(date_group)
        
        # منطقة أنواع التقارير
        reports_types_group = QGroupBox("أنواع التقارير المتاحة")
        reports_types_layout = QGridLayout(reports_types_group)
        
        # تقارير المبيعات
        sales_reports_group = QGroupBox("تقارير المبيعات")
        sales_layout = QVBoxLayout(sales_reports_group)
        
        sales_summary_btn = QPushButton("📈 تقرير ملخص المبيعات")
        sales_summary_btn.clicked.connect(self.generate_sales_summary)
        sales_layout.addWidget(sales_summary_btn)
        
        sales_detailed_btn = QPushButton("📋 تقرير المبيعات التفصيلي")
        sales_detailed_btn.clicked.connect(self.generate_sales_detailed)
        sales_layout.addWidget(sales_detailed_btn)
        
        customer_sales_btn = QPushButton("👥 تقرير مبيعات الزبائن")
        customer_sales_btn.clicked.connect(self.generate_customer_sales)
        sales_layout.addWidget(customer_sales_btn)
        
        reports_types_layout.addWidget(sales_reports_group, 0, 0)
        
        # تقارير المشتريات
        purchases_reports_group = QGroupBox("تقارير المشتريات")
        purchases_layout = QVBoxLayout(purchases_reports_group)
        
        purchases_summary_btn = QPushButton("🛒 تقرير ملخص المشتريات")
        purchases_summary_btn.clicked.connect(self.generate_purchases_summary)
        purchases_layout.addWidget(purchases_summary_btn)
        
        suppliers_report_btn = QPushButton("🏪 تقرير الموردين")
        suppliers_report_btn.clicked.connect(self.generate_suppliers_report)
        purchases_layout.addWidget(suppliers_report_btn)
        
        reports_types_layout.addWidget(purchases_reports_group, 0, 1)
        
        # تقارير المخزون
        inventory_reports_group = QGroupBox("تقارير المخزون")
        inventory_layout = QVBoxLayout(inventory_reports_group)
        
        stock_report_btn = QPushButton("📦 تقرير المخزون الحالي")
        stock_report_btn.clicked.connect(self.generate_stock_report)
        inventory_layout.addWidget(stock_report_btn)
        
        low_stock_btn = QPushButton("⚠️ تقرير المواد المنخفضة")
        low_stock_btn.clicked.connect(self.generate_low_stock_report)
        inventory_layout.addWidget(low_stock_btn)
        
        reports_types_layout.addWidget(inventory_reports_group, 1, 0)
        
        # تقارير مالية
        financial_reports_group = QGroupBox("التقارير المالية")
        financial_layout = QVBoxLayout(financial_reports_group)
        
        profit_loss_btn = QPushButton("💰 تقرير الأرباح والخسائر")
        profit_loss_btn.clicked.connect(self.generate_profit_loss)
        financial_layout.addWidget(profit_loss_btn)
        
        debts_report_btn = QPushButton("💳 تقرير المديونية")
        debts_report_btn.clicked.connect(self.generate_debts_report)
        financial_layout.addWidget(debts_report_btn)
        
        reports_types_layout.addWidget(financial_reports_group, 1, 1)
        
        reports_layout.addWidget(reports_types_group)
        
        return reports_frame

    def create_control_buttons(self):
        """إنشاء أزرار التحكم السفلية"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)

        # زر فتح مجلد التقارير
        open_reports_folder_btn = QPushButton("📁 فتح مجلد التقارير")
        open_reports_folder_btn.clicked.connect(self.open_reports_folder)
        open_reports_folder_btn.setStyleSheet(f"background: {self.colors['success']};")

        # زر العودة
        back_btn = QPushButton("🔙 العودة للقائمة الرئيسية")
        back_btn.clicked.connect(self.close)
        back_btn.setStyleSheet(f"background: {self.colors['dark']};")

        buttons_layout.addStretch()
        buttons_layout.addWidget(open_reports_folder_btn)
        buttons_layout.addWidget(back_btn)

        return buttons_frame

    def get_date_range(self):
        """الحصول على الفترة الزمنية المحددة"""
        start_date = self.start_date.date().toPyDate()
        end_date = self.end_date.date().toPyDate()

        if start_date > end_date:
            QMessageBox.warning(self, "خطأ", "تاريخ البداية يجب أن يكون قبل تاريخ النهاية")
            return None, None

        return start_date, end_date

    def show_progress(self, message="جاري إنشاء التقرير..."):
        """عرض شريط التقدم"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # شريط تقدم غير محدد
        self.progress_bar.setFormat(message)

    def hide_progress(self):
        """إخفاء شريط التقدم"""
        self.progress_bar.setVisible(False)

    def open_reports_folder(self):
        """فتح مجلد التقارير"""
        reports_folder = "reports"
        if not os.path.exists(reports_folder):
            os.makedirs(reports_folder)

        try:
            os.startfile(reports_folder)  # Windows
        except:
            try:
                os.system(f"open {reports_folder}")  # macOS
            except:
                os.system(f"xdg-open {reports_folder}")  # Linux

    # دوال توليد التقارير
    def generate_sales_summary(self):
        """توليد تقرير ملخص المبيعات"""
        start_date, end_date = self.get_date_range()
        if not start_date or not end_date:
            return

        self.show_progress("جاري إنشاء تقرير ملخص المبيعات...")

        try:
            # جلب بيانات المبيعات
            query = """
                SELECT
                    COUNT(*) as total_invoices,
                    SUM(total_amount) as total_sales,
                    SUM(paid_amount) as total_paid,
                    SUM(total_amount - paid_amount) as total_remaining,
                    AVG(total_amount) as avg_invoice
                FROM sales_invoices
                WHERE invoice_date BETWEEN ? AND ?
            """

            summary_data = db.execute_query(query, (start_date, end_date))

            if summary_data:
                data = summary_data[0]

                # إنشاء التقرير
                report_content = f"""
تقرير ملخص المبيعات
من {start_date} إلى {end_date}

إجمالي عدد الفواتير: {data['total_invoices'] or 0}
إجمالي المبيعات: {data['total_sales'] or 0:.2f} ريال
إجمالي المدفوع: {data['total_paid'] or 0:.2f} ريال
إجمالي المتبقي: {data['total_remaining'] or 0:.2f} ريال
متوسط قيمة الفاتورة: {data['avg_invoice'] or 0:.2f} ريال
                """

                # حفظ التقرير
                filename = f"reports/sales_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
                os.makedirs("reports", exist_ok=True)

                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(report_content)

                QMessageBox.information(self, "تم", f"تم إنشاء التقرير بنجاح\n{filename}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء التقرير: {str(e)}")

        finally:
            self.hide_progress()

    def generate_sales_detailed(self):
        """توليد تقرير المبيعات التفصيلي"""
        start_date, end_date = self.get_date_range()
        if not start_date or not end_date:
            return

        self.show_progress("جاري إنشاء تقرير المبيعات التفصيلي...")

        try:
            # جلب بيانات المبيعات التفصيلية
            query = """
                SELECT
                    si.id,
                    si.invoice_date,
                    c.name as customer_name,
                    si.total_amount,
                    si.paid_amount,
                    (si.total_amount - si.paid_amount) as remaining,
                    si.trays_taken,
                    si.notes
                FROM sales_invoices si
                LEFT JOIN customers c ON si.customer_id = c.id
                WHERE si.invoice_date BETWEEN ? AND ?
                ORDER BY si.invoice_date DESC
            """

            sales_data = db.execute_query(query, (start_date, end_date))

            # إنشاء التقرير
            report_content = f"تقرير المبيعات التفصيلي\nمن {start_date} إلى {end_date}\n\n"

            total_amount = 0
            total_paid = 0

            for sale in sales_data:
                total_amount += sale['total_amount']
                total_paid += sale['paid_amount']

                report_content += f"""
رقم الفاتورة: {sale['id']}
التاريخ: {sale['invoice_date']}
الزبون: {sale['customer_name'] or 'غير محدد'}
المبلغ الإجمالي: {sale['total_amount']:.2f} ريال
المبلغ المدفوع: {sale['paid_amount']:.2f} ريال
المتبقي: {sale['remaining']:.2f} ريال
الصواني المأخوذة: {sale['trays_taken']}
الملاحظات: {sale['notes'] or 'لا توجد'}
{'='*50}
                """

            report_content += f"""

الملخص:
إجمالي المبيعات: {total_amount:.2f} ريال
إجمالي المدفوع: {total_paid:.2f} ريال
إجمالي المتبقي: {total_amount - total_paid:.2f} ريال
            """

            # حفظ التقرير
            filename = f"reports/sales_detailed_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            os.makedirs("reports", exist_ok=True)

            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report_content)

            QMessageBox.information(self, "تم", f"تم إنشاء التقرير بنجاح\n{filename}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء التقرير: {str(e)}")

        finally:
            self.hide_progress()

    def generate_customer_sales(self):
        """توليد تقرير مبيعات الزبائن"""
        QMessageBox.information(self, "قريباً", "سيتم إضافة تقرير مبيعات الزبائن قريباً")

    def generate_purchases_summary(self):
        """توليد تقرير ملخص المشتريات"""
        QMessageBox.information(self, "قريباً", "سيتم إضافة تقرير ملخص المشتريات قريباً")

    def generate_suppliers_report(self):
        """توليد تقرير الموردين"""
        QMessageBox.information(self, "قريباً", "سيتم إضافة تقرير الموردين قريباً")

    def generate_stock_report(self):
        """توليد تقرير المخزون الحالي"""
        QMessageBox.information(self, "قريباً", "سيتم إضافة تقرير المخزون الحالي قريباً")

    def generate_low_stock_report(self):
        """توليد تقرير المواد المنخفضة"""
        QMessageBox.information(self, "قريباً", "سيتم إضافة تقرير المواد المنخفضة قريباً")

    def generate_profit_loss(self):
        """توليد تقرير الأرباح والخسائر"""
        QMessageBox.information(self, "قريباً", "سيتم إضافة تقرير الأرباح والخسائر قريباً")

    def generate_debts_report(self):
        """توليد تقرير المديونية"""
        self.show_progress("جاري إنشاء تقرير المديونية...")

        try:
            # جلب بيانات المديونية
            query = """
                SELECT
                    c.name,
                    c.phone,
                    COALESCE(SUM(si.total_amount), 0) as total_sales,
                    COALESCE(SUM(si.paid_amount), 0) as total_paid,
                    COALESCE(SUM(si.total_amount - si.paid_amount), 0) as remaining,
                    MAX(si.invoice_date) as last_invoice_date
                FROM customers c
                LEFT JOIN sales_invoices si ON c.id = si.customer_id
                GROUP BY c.id, c.name, c.phone
                HAVING remaining > 0
                ORDER BY remaining DESC
            """

            debts_data = db.execute_query(query)

            # إنشاء التقرير
            report_content = f"تقرير المديونية\nتاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

            total_debts = 0
            customers_count = 0

            for debt in debts_data:
                if debt['remaining'] > 0:
                    customers_count += 1
                    total_debts += debt['remaining']

                    report_content += f"""
الزبون: {debt['name']}
رقم الهاتف: {debt['phone'] or 'غير محدد'}
إجمالي المبيعات: {debt['total_sales']:.2f} ريال
إجمالي المدفوع: {debt['total_paid']:.2f} ريال
المتبقي: {debt['remaining']:.2f} ريال
آخر فاتورة: {debt['last_invoice_date'] or 'غير محدد'}
{'='*50}
                    """

            report_content += f"""

الملخص:
عدد الزبائن المدينين: {customers_count}
إجمالي الديون: {total_debts:.2f} ريال
            """

            # حفظ التقرير
            filename = f"reports/debts_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            os.makedirs("reports", exist_ok=True)

            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report_content)

            QMessageBox.information(self, "تم", f"تم إنشاء التقرير بنجاح\n{filename}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء التقرير: {str(e)}")

        finally:
            self.hide_progress()
