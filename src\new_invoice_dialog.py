#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إنشاء فاتورة مشتريات جديدة - نسخة نظيفة
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                             QPushButton, QLabel, QLineEdit, QComboBox, 
                             QTableWidget, QTableWidgetItem, QMessageBox,
                             QGroupBox, QDateEdit, QTextEdit, QHeaderView)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont, QColor
from database import db

class NewInvoiceDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إنشاء فاتورة مشتريات جديدة")
        self.setFixedSize(1000, 600)
        
        # قائمة المواد المضافة
        self.invoice_items = []
        
        # توسيط النافذة
        self.center_window()
        
        # إنشاء الواجهة
        self.setup_ui()
        
        # تحميل البيانات
        self.load_suppliers()
        self.load_materials()
        self.generate_invoice_number()

    def center_window(self):
        """توسيط النافذة"""
        from PyQt5.QtWidgets import QApplication
        screen = QApplication.desktop().screenGeometry()
        x = (screen.width() - self.width()) // 2
        y = (screen.height() - self.height()) // 2
        self.move(x, y)

    def setup_ui(self):
        """إنشاء واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان
        title = QLabel("إنشاء فاتورة مشتريات جديدة")
        title.setFont(QFont("Tahoma", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #2C3E50; padding: 10px;")
        layout.addWidget(title)
        
        # معلومات الفاتورة
        self.create_invoice_info_section(layout)
        
        # إضافة المواد
        self.create_materials_section(layout)
        
        # جدول المواد
        self.create_items_table(layout)
        
        # المبالغ المالية
        self.create_amounts_section(layout)
        
        # أزرار التحكم
        self.create_buttons_section(layout)

    def create_invoice_info_section(self, parent_layout):
        """إنشاء قسم معلومات الفاتورة"""
        group = QGroupBox("معلومات الفاتورة")
        layout = QGridLayout(group)
        
        # رقم الفاتورة
        layout.addWidget(QLabel("رقم الفاتورة:"), 0, 0)
        self.invoice_number_edit = QLineEdit()
        self.invoice_number_edit.setFixedHeight(25)
        layout.addWidget(self.invoice_number_edit, 0, 1)
        
        # التاريخ
        layout.addWidget(QLabel("تاريخ الفاتورة:"), 0, 2)
        self.invoice_date_edit = QDateEdit()
        self.invoice_date_edit.setDate(QDate.currentDate())
        self.invoice_date_edit.setFixedHeight(25)
        layout.addWidget(self.invoice_date_edit, 0, 3)
        
        # المورد
        layout.addWidget(QLabel("المورد:"), 1, 0)
        self.supplier_combo = QComboBox()
        self.supplier_combo.setFixedHeight(25)
        layout.addWidget(self.supplier_combo, 1, 1, 1, 3)
        
        parent_layout.addWidget(group)

    def create_materials_section(self, parent_layout):
        """إنشاء قسم إضافة المواد"""
        group = QGroupBox("إضافة المواد")
        layout = QHBoxLayout(group)
        
        # المادة
        layout.addWidget(QLabel("المادة:"))
        self.material_combo = QComboBox()
        self.material_combo.setFixedHeight(25)
        self.material_combo.setMinimumWidth(200)
        layout.addWidget(self.material_combo)
        
        # الكمية
        layout.addWidget(QLabel("الكمية:"))
        self.quantity_edit = QLineEdit("1.00")
        self.quantity_edit.setFixedHeight(25)
        self.quantity_edit.setFixedWidth(80)
        layout.addWidget(self.quantity_edit)
        
        # الوحدة
        layout.addWidget(QLabel("الوحدة:"))
        self.unit_combo = QComboBox()
        self.unit_combo.addItems(["كيلو", "غرام", "لتر", "قطعة", "علبة", "كيس"])
        self.unit_combo.setFixedHeight(25)
        self.unit_combo.setFixedWidth(80)
        layout.addWidget(self.unit_combo)
        
        # السعر
        layout.addWidget(QLabel("السعر:"))
        self.unit_price_edit = QLineEdit("0.00")
        self.unit_price_edit.setFixedHeight(25)
        self.unit_price_edit.setFixedWidth(80)
        layout.addWidget(self.unit_price_edit)
        
        # زر الإضافة
        self.add_btn = QPushButton("إضافة")
        self.add_btn.setFixedHeight(25)
        self.add_btn.setFixedWidth(80)
        self.add_btn.setStyleSheet("background: #27AE60; color: white; font-weight: bold;")
        self.add_btn.clicked.connect(self.add_material)
        layout.addWidget(self.add_btn)
        
        parent_layout.addWidget(group)

    def create_items_table(self, parent_layout):
        """إنشاء جدول المواد"""
        group = QGroupBox("المواد المضافة")
        layout = QVBoxLayout(group)
        
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(5)
        self.items_table.setHorizontalHeaderLabels([
            "المادة", "الكمية", "الوحدة", "السعر", "الإجمالي"
        ])
        
        # تعديل عرض الأعمدة
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        
        self.items_table.setMaximumHeight(200)
        layout.addWidget(self.items_table)
        
        parent_layout.addWidget(group)

    def create_amounts_section(self, parent_layout):
        """إنشاء قسم المبالغ المالية"""
        group = QGroupBox("المبالغ المالية")
        layout = QGridLayout(group)
        
        # المبلغ الإجمالي
        layout.addWidget(QLabel("المبلغ الإجمالي:"), 0, 0)
        self.total_amount_edit = QLineEdit("0.00")
        self.total_amount_edit.setReadOnly(True)
        self.total_amount_edit.setFixedHeight(25)
        self.total_amount_edit.setStyleSheet("background: #ECF0F1; font-weight: bold;")
        layout.addWidget(self.total_amount_edit, 0, 1)
        
        # المبلغ المدفوع
        layout.addWidget(QLabel("المبلغ المدفوع:"), 0, 2)
        self.paid_amount_edit = QLineEdit("0.00")
        self.paid_amount_edit.setFixedHeight(25)
        layout.addWidget(self.paid_amount_edit, 0, 3)
        
        # طريقة الدفع
        layout.addWidget(QLabel("طريقة الدفع:"), 1, 0)
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItems(["نقداً", "شيك", "تحويل بنكي", "آجل"])
        self.payment_method_combo.setFixedHeight(25)
        layout.addWidget(self.payment_method_combo, 1, 1)
        
        parent_layout.addWidget(group)

    def create_buttons_section(self, parent_layout):
        """إنشاء قسم الأزرار"""
        layout = QHBoxLayout()
        
        # زر المعاينة
        preview_btn = QPushButton("معاينة")
        preview_btn.setFixedHeight(35)
        preview_btn.setStyleSheet("background: #3498DB; color: white; font-weight: bold; padding: 5px 15px;")
        preview_btn.clicked.connect(self.preview_invoice)
        layout.addWidget(preview_btn)
        
        # زر الحفظ
        save_btn = QPushButton("حفظ الفاتورة")
        save_btn.setFixedHeight(35)
        save_btn.setStyleSheet("background: #27AE60; color: white; font-weight: bold; padding: 5px 15px;")
        save_btn.clicked.connect(self.save_invoice)
        layout.addWidget(save_btn)
        
        # زر الإلغاء
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setFixedHeight(35)
        cancel_btn.setStyleSheet("background: #95A5A6; color: white; font-weight: bold; padding: 5px 15px;")
        cancel_btn.clicked.connect(self.reject)
        layout.addWidget(cancel_btn)
        
        layout.addStretch()
        parent_layout.addLayout(layout)

    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        try:
            suppliers = db.execute_query("SELECT id, name FROM suppliers ORDER BY name")
            self.supplier_combo.clear()
            self.supplier_combo.addItem("اختر المورد", 0)
            for supplier in suppliers:
                self.supplier_combo.addItem(supplier['name'], supplier['id'])
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الموردين: {str(e)}")

    def load_materials(self):
        """تحميل قائمة المواد"""
        try:
            materials = db.execute_query("SELECT id, name, unit, price FROM raw_materials ORDER BY name")
            self.material_combo.clear()
            self.material_combo.addItem("اختر المادة", 0)
            for material in materials:
                display_text = f"{material['name']} ({material['unit']})"
                self.material_combo.addItem(display_text, material['id'])
            self.material_combo.currentIndexChanged.connect(self.update_material_price)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل المواد: {str(e)}")

    def generate_invoice_number(self):
        """توليد رقم فاتورة تلقائي"""
        try:
            from datetime import datetime
            last_invoice = db.execute_query("""
                SELECT invoice_number FROM purchase_invoices
                WHERE invoice_number LIKE 'PUR-%'
                ORDER BY id DESC LIMIT 1
            """)

            if last_invoice:
                last_number = last_invoice[0]['invoice_number']
                try:
                    parts = last_number.split('-')
                    if len(parts) >= 3:
                        last_seq = int(parts[2])
                        new_seq = last_seq + 1
                    else:
                        new_seq = 1
                except:
                    new_seq = 1
            else:
                new_seq = 1

            current_year = datetime.now().year
            invoice_number = f"PUR-{current_year}-{new_seq:04d}"
            self.invoice_number_edit.setText(invoice_number)
        except Exception:
            from datetime import datetime
            current_year = datetime.now().year
            invoice_number = f"PUR-{current_year}-0001"
            self.invoice_number_edit.setText(invoice_number)

    def update_material_price(self):
        """تحديث سعر المادة عند اختيارها"""
        material_id = self.material_combo.currentData()
        if material_id and material_id != 0:
            try:
                material = db.execute_query("SELECT price FROM raw_materials WHERE id = ?", (material_id,))
                if material:
                    self.unit_price_edit.setText(f"{material[0]['price']:.2f}")
            except:
                pass

    def add_material(self):
        """إضافة مادة للفاتورة"""
        try:
            # التحقق من البيانات
            material_text = self.material_combo.currentText()
            if not material_text or material_text == "اختر المادة":
                QMessageBox.warning(self, "تحذير", "يرجى اختيار المادة")
                return

            try:
                quantity = float(self.quantity_edit.text())
                unit_price = float(self.unit_price_edit.text())
            except ValueError:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال قيم رقمية صحيحة")
                return

            if quantity <= 0 or unit_price <= 0:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال قيم أكبر من الصفر")
                return

            # إنشاء العنصر
            unit = self.unit_combo.currentText()
            total_price = quantity * unit_price

            item = {
                'material_name': material_text,
                'quantity': quantity,
                'unit': unit,
                'unit_price': unit_price,
                'total_price': total_price
            }

            # إضافة للقائمة
            self.invoice_items.append(item)

            # تحديث الجدول
            self.update_table()

            # حساب المجموع
            self.calculate_total()

            # إعادة تعيين الحقول
            self.material_combo.setCurrentIndex(0)
            self.quantity_edit.setText("1.00")
            self.unit_price_edit.setText("0.00")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة المادة: {str(e)}")

    def update_table(self):
        """تحديث جدول المواد"""
        self.items_table.setRowCount(len(self.invoice_items))

        for row, item in enumerate(self.invoice_items):
            self.items_table.setItem(row, 0, QTableWidgetItem(str(item['material_name'])))
            self.items_table.setItem(row, 1, QTableWidgetItem(f"{item['quantity']:.2f}"))
            self.items_table.setItem(row, 2, QTableWidgetItem(str(item['unit'])))
            self.items_table.setItem(row, 3, QTableWidgetItem(f"{item['unit_price']:.2f}"))
            self.items_table.setItem(row, 4, QTableWidgetItem(f"{item['total_price']:.2f}"))

    def calculate_total(self):
        """حساب المجموع الإجمالي"""
        total = sum(item['total_price'] for item in self.invoice_items)
        self.total_amount_edit.setText(f"{total:.2f}")

    def preview_invoice(self):
        """معاينة الفاتورة"""
        if not self.invoice_items:
            QMessageBox.warning(self, "تحذير", "لا توجد مواد في الفاتورة")
            return

        preview_text = "معاينة الفاتورة\n" + "="*30 + "\n\n"
        preview_text += f"رقم الفاتورة: {self.invoice_number_edit.text()}\n"
        preview_text += f"المورد: {self.supplier_combo.currentText()}\n"
        preview_text += f"التاريخ: {self.invoice_date_edit.date().toString('yyyy-MM-dd')}\n\n"

        preview_text += "المواد:\n" + "-"*20 + "\n"
        for i, item in enumerate(self.invoice_items, 1):
            preview_text += f"{i}. {item['material_name']}\n"
            preview_text += f"   {item['quantity']:.2f} {item['unit']} × {item['unit_price']:.2f} = {item['total_price']:.2f} دج\n\n"

        preview_text += f"المبلغ الإجمالي: {self.total_amount_edit.text()} دج\n"
        preview_text += f"المبلغ المدفوع: {self.paid_amount_edit.text()} دج\n"
        preview_text += f"طريقة الدفع: {self.payment_method_combo.currentText()}"

        QMessageBox.information(self, "معاينة الفاتورة", preview_text)

    def save_invoice(self):
        """حفظ الفاتورة"""
        if not self.invoice_items:
            QMessageBox.warning(self, "تحذير", "لا يمكن حفظ فاتورة فارغة")
            return

        supplier_id = self.supplier_combo.currentData()
        if not supplier_id or supplier_id == 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار المورد")
            return

        reply = QMessageBox.question(
            self,
            "تأكيد الحفظ",
            "هل تريد حفظ الفاتورة؟",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "نجح", "تم حفظ الفاتورة بنجاح!")
            self.accept()
