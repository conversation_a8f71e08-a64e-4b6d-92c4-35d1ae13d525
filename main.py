#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج محاسبة مصنع الحلويات التقليدية
نظام إدارة متكامل للمحاسبة والإنتاج والمبيعات

المطور: Augment Agent
التاريخ: 2025-06-18
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt, QTranslator, QLocale
from PyQt5.QtGui import QFont

# إضافة مجلد src إلى المسار
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from main_window import MainWindow

def main():
    """الدالة الرئيسية لتشغيل البرنامج"""
    app = QApplication(sys.argv)
    
    # إعداد اللغة العربية والاتجاه من اليمين لليسار
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إعداد الخط العربي
    font = QFont("Arial", 10)
    font.setFamily("Tahoma")
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
