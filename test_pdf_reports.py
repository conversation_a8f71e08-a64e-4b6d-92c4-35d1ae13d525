#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار توليد تقارير PDF مع دعم الكتابة من اليمين لليسار
"""

import sys
import os
from datetime import datetime, date

# إضافة مجلد src إلى المسار
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from database import db

def test_pdf_generation():
    """اختبار توليد PDF"""
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        from reportlab.lib.units import inch
        from bidi.algorithm import get_display
        import arabic_reshaper
        
        print("✅ جميع مكتبات PDF متوفرة")
        return True
        
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        print("يرجى تثبيت المكتبات المطلوبة:")
        print("pip install reportlab python-bidi arabic-reshaper")
        return False

def create_sample_pdf_report():
    """إنشاء تقرير PDF تجريبي"""
    if not test_pdf_generation():
        return False
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        from reportlab.lib.units import inch
        from bidi.algorithm import get_display
        import arabic_reshaper
        
        # إنشاء مجلد التقارير
        os.makedirs("reports", exist_ok=True)
        
        # اسم الملف
        filename = f"reports/sample_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        
        # إنشاء PDF
        c = canvas.Canvas(filename, pagesize=A4)
        width, height = A4
        
        # محاولة تحميل خط عربي (إذا كان متوفراً)
        try:
            # يمكن تحميل خط عربي من النظام
            font_path = "C:/Windows/Fonts/arial.ttf"  # Windows
            if os.path.exists(font_path):
                pdfmetrics.registerFont(TTFont('Arabic', font_path))
                font_name = 'Arabic'
            else:
                font_name = 'Helvetica'  # خط افتراضي
        except:
            font_name = 'Helvetica'
        
        # عنوان التقرير
        title = "تقرير المبيعات التجريبي"
        
        # معالجة النص العربي للعرض الصحيح
        try:
            reshaped_title = arabic_reshaper.reshape(title)
            bidi_title = get_display(reshaped_title)
        except:
            bidi_title = title
        
        # كتابة العنوان
        c.setFont(font_name, 20)
        c.drawRightString(width - 50, height - 100, bidi_title)
        
        # معلومات التقرير
        y_position = height - 150
        
        report_data = [
            f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "نوع التقرير: تقرير مبيعات تجريبي",
            "الفترة: من 2025-06-01 إلى 2025-06-18",
            "",
            "البيانات التجريبية:",
            "إجمالي المبيعات: 1,250.00 ريال",
            "عدد الفواتير: 15 فاتورة",
            "متوسط قيمة الفاتورة: 83.33 ريال",
            "",
            "أفضل المنتجات مبيعاً:",
            "1. معمول التمر - 450.00 ريال",
            "2. كعك العسل - 320.00 ريال", 
            "3. بسكويت اللوز - 280.00 ريال",
            "",
            "ملاحظات:",
            "- تم إنشاء هذا التقرير تلقائياً",
            "- البيانات المعروضة تجريبية",
            "- يدعم التقرير الكتابة من اليمين لليسار"
        ]
        
        # كتابة البيانات
        c.setFont(font_name, 12)
        for line in report_data:
            if line.strip():
                try:
                    reshaped_line = arabic_reshaper.reshape(line)
                    bidi_line = get_display(reshaped_line)
                except:
                    bidi_line = line
                
                c.drawRightString(width - 50, y_position, bidi_line)
            y_position -= 20
            
            # إضافة صفحة جديدة إذا لزم الأمر
            if y_position < 100:
                c.showPage()
                y_position = height - 100
                c.setFont(font_name, 12)
        
        # إضافة تذييل
        c.setFont(font_name, 10)
        footer_text = "برنامج محاسبة مصنع الحلويات التقليدية - تم الإنشاء بواسطة Augment Agent"
        try:
            reshaped_footer = arabic_reshaper.reshape(footer_text)
            bidi_footer = get_display(reshaped_footer)
        except:
            bidi_footer = footer_text
        
        c.drawRightString(width - 50, 50, bidi_footer)
        
        # حفظ PDF
        c.save()
        
        print(f"✅ تم إنشاء تقرير PDF بنجاح: {filename}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء تقرير PDF: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sales_report_with_real_data():
    """اختبار تقرير مبيعات بالبيانات الحقيقية"""
    if not test_pdf_generation():
        return False
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        from reportlab.lib.units import inch
        from bidi.algorithm import get_display
        import arabic_reshaper
        
        # جلب بيانات المبيعات من قاعدة البيانات
        query = """
            SELECT
                si.id,
                si.invoice_date,
                c.name as customer_name,
                si.total_amount,
                si.paid_amount,
                (si.total_amount - si.paid_amount) as remaining
            FROM sales_invoices si
            LEFT JOIN customers c ON si.customer_id = c.id
            ORDER BY si.invoice_date DESC
            LIMIT 10
        """
        
        sales_data = db.execute_query(query)
        
        if not sales_data:
            print("لا توجد بيانات مبيعات في قاعدة البيانات")
            return False
        
        # إنشاء مجلد التقارير
        os.makedirs("reports", exist_ok=True)
        
        # اسم الملف
        filename = f"reports/real_sales_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        
        # إنشاء PDF
        c = canvas.Canvas(filename, pagesize=A4)
        width, height = A4
        
        # محاولة تحميل خط عربي
        try:
            font_path = "C:/Windows/Fonts/arial.ttf"
            if os.path.exists(font_path):
                pdfmetrics.registerFont(TTFont('Arabic', font_path))
                font_name = 'Arabic'
            else:
                font_name = 'Helvetica'
        except:
            font_name = 'Helvetica'
        
        # عنوان التقرير
        title = "تقرير المبيعات الفعلي"
        try:
            reshaped_title = arabic_reshaper.reshape(title)
            bidi_title = get_display(reshaped_title)
        except:
            bidi_title = title
        
        c.setFont(font_name, 20)
        c.drawRightString(width - 50, height - 100, bidi_title)
        
        # معلومات التقرير
        y_position = height - 150
        
        # حساب الإجماليات
        total_sales = sum(sale['total_amount'] for sale in sales_data)
        total_paid = sum(sale['paid_amount'] for sale in sales_data)
        total_remaining = total_sales - total_paid
        
        header_info = [
            f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"عدد الفواتير: {len(sales_data)}",
            f"إجمالي المبيعات: {total_sales:.2f} ريال",
            f"إجمالي المدفوع: {total_paid:.2f} ريال",
            f"إجمالي المتبقي: {total_remaining:.2f} ريال",
            "",
            "تفاصيل الفواتير:"
        ]
        
        c.setFont(font_name, 12)
        for line in header_info:
            if line.strip():
                try:
                    reshaped_line = arabic_reshaper.reshape(line)
                    bidi_line = get_display(reshaped_line)
                except:
                    bidi_line = line
                
                c.drawRightString(width - 50, y_position, bidi_line)
            y_position -= 20
        
        # تفاصيل الفواتير
        for sale in sales_data:
            invoice_details = [
                f"رقم الفاتورة: {sale['id']}",
                f"التاريخ: {sale['invoice_date']}",
                f"الزبون: {sale['customer_name'] or 'غير محدد'}",
                f"المبلغ: {sale['total_amount']:.2f} ريال",
                f"المدفوع: {sale['paid_amount']:.2f} ريال",
                f"المتبقي: {sale['remaining']:.2f} ريال",
                "---"
            ]
            
            for detail in invoice_details:
                try:
                    reshaped_detail = arabic_reshaper.reshape(detail)
                    bidi_detail = get_display(reshaped_detail)
                except:
                    bidi_detail = detail
                
                c.drawRightString(width - 50, y_position, bidi_detail)
                y_position -= 15
                
                # إضافة صفحة جديدة إذا لزم الأمر
                if y_position < 100:
                    c.showPage()
                    y_position = height - 100
                    c.setFont(font_name, 12)
        
        # إضافة تذييل
        c.setFont(font_name, 10)
        footer_text = "برنامج محاسبة مصنع الحلويات التقليدية"
        try:
            reshaped_footer = arabic_reshaper.reshape(footer_text)
            bidi_footer = get_display(reshaped_footer)
        except:
            bidi_footer = footer_text
        
        c.drawRightString(width - 50, 50, bidi_footer)
        
        # حفظ PDF
        c.save()
        
        print(f"✅ تم إنشاء تقرير المبيعات الفعلي بنجاح: {filename}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء تقرير المبيعات الفعلي: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية لاختبار تقارير PDF"""
    print("اختبار توليد تقارير PDF مع دعم العربية")
    print("=" * 50)
    
    # اختبار توفر المكتبات
    if not test_pdf_generation():
        print("\n❌ لا يمكن إنشاء تقارير PDF - مكتبات مفقودة")
        return
    
    print("\n1. إنشاء تقرير PDF تجريبي...")
    if create_sample_pdf_report():
        print("✅ تم إنشاء التقرير التجريبي بنجاح")
    else:
        print("❌ فشل في إنشاء التقرير التجريبي")
    
    print("\n2. إنشاء تقرير مبيعات بالبيانات الفعلية...")
    if test_sales_report_with_real_data():
        print("✅ تم إنشاء تقرير المبيعات الفعلي بنجاح")
    else:
        print("❌ فشل في إنشاء تقرير المبيعات الفعلي")
    
    print("\n" + "=" * 50)
    print("انتهى اختبار تقارير PDF")

if __name__ == "__main__":
    main()
