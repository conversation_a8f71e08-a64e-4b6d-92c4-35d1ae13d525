# برنامج محاسبة مصنع الحلويات التقليدية

## نظرة عامة
برنامج محاسبة وإدارة إنتاج متكامل مخصص لمصانع الحلويات التقليدية، مطور بلغة Python باستخدام PyQt5 مع دعم كامل للغة العربية والكتابة من اليمين لليسار.

## المميزات الرئيسية
- 🏭 إدارة الإنتاج والوصفات
- 🛒 إدارة المشتريات والموردين
- 💰 إدارة المبيعات والزبائن
- 📋 نظام الطلبيات المتقدم
- 👥 إدارة العمال والأجور
- 📦 إدارة المخازن والمواد
- 📊 تقارير مالية شاملة
- 💾 نسخ احتياطي واسترجاع

## المتطلبات التقنية
- Python 3.8+
- PyQt5
- SQLite
- ReportLab
- Windows 7+

## التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2. تشغيل البرنامج
```bash
python main.py
```

### 3. إنشاء ملف exe
```bash
pyinstaller --onefile --windowed --add-data "assets;assets" --add-data "database;database" main.py
```

## هيكل المشروع
```
برنامج محاسبة للحلويات/
├── main.py                 # الملف الرئيسي
├── requirements.txt        # المتطلبات
├── src/                   # الكود المصدري
├── database/              # قاعدة البيانات
├── assets/                # الصور والموارد
├── ui/                    # ملفات الواجهة
├── reports/               # التقارير المُصدرة
└── backup/                # النسخ الاحتياطية
```

## الاستخدام
1. تشغيل البرنامج من الملف الرئيسي
2. إعداد البيانات الأساسية (موردين، مواد، منتجات)
3. تسجيل العمليات اليومية
4. استخراج التقارير المطلوبة

## الدعم الفني
للمساعدة والدعم الفني، يرجى مراجعة دليل المستخدم المرفق مع البرنامج.

## الترخيص
هذا البرنامج مطور خصيصاً لمصانع الحلويات التقليدية.

---
**تم التطوير بواسطة:** Augment Agent  
**التاريخ:** 2025-06-18
