#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لإضافة مورد
"""

import sys
import os
sys.path.append('src')

def test_add_supplier_direct():
    """اختبار إضافة مورد مباشرة في قاعدة البيانات"""
    print("🔄 اختبار إضافة مورد مباشرة...")
    
    try:
        from database import db
        
        # إضافة مورد جديد
        query = """
            INSERT INTO suppliers (name, phone, address, notes)
            VALUES (?, ?, ?, ?)
        """
        
        supplier_id = db.execute_insert(query, (
            "شركة الاختبار",
            "0501234567",
            "الرياض - حي الاختبار", 
            "مورد للاختبار"
        ))
        
        print(f"✅ تم إضافة المورد بنجاح برقم: {supplier_id}")
        
        # التحقق من الإضافة
        suppliers = db.execute_query("SELECT * FROM suppliers WHERE id = ?", (supplier_id,))
        if suppliers:
            supplier = suppliers[0]
            print(f"✅ تم التحقق من المورد: {supplier['name']}")
            return True
        else:
            print("❌ لم يتم العثور على المورد المضاف")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إضافة المورد: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_add_material_direct():
    """اختبار إضافة مادة مباشرة في قاعدة البيانات"""
    print("🔄 اختبار إضافة مادة مباشرة...")
    
    try:
        from database import db
        
        # إضافة مادة جديدة
        query = """
            INSERT INTO raw_materials (name, unit, price, stock_quantity, min_stock, notes)
            VALUES (?, ?, ?, ?, ?, ?)
        """
        
        material_id = db.execute_insert(query, (
            "دقيق الاختبار",
            "كيلوغرام",
            3.50,
            100.0,
            10.0,
            "مادة للاختبار"
        ))
        
        print(f"✅ تم إضافة المادة بنجاح برقم: {material_id}")
        
        # التحقق من الإضافة
        materials = db.execute_query("SELECT * FROM raw_materials WHERE id = ?", (material_id,))
        if materials:
            material = materials[0]
            print(f"✅ تم التحقق من المادة: {material['name']}")
            return True
        else:
            print("❌ لم يتم العثور على المادة المضافة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إضافة المادة: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 40)
    print("🧪 اختبار بسيط لإضافة المشتريات")
    print("=" * 40)
    
    # اختبار إضافة مورد
    if test_add_supplier_direct():
        print("✅ اختبار المورد نجح")
    else:
        print("❌ اختبار المورد فشل")
    
    print()
    
    # اختبار إضافة مادة
    if test_add_material_direct():
        print("✅ اختبار المادة نجح")
    else:
        print("❌ اختبار المادة فشل")
    
    print("\n" + "=" * 40)
    print("📝 الآن جرب فتح البرنامج واختبار إضافة المشتريات من الواجهة")
    print("🚀 شغل: python main.py")

if __name__ == "__main__":
    main()
