#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة الإعدادات
إدارة النسخ الاحتياطي والاسترجاع وإعادة ضبط البرنامج
"""

import os
import shutil
from datetime import datetime
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QGridLayout, QPushButton, QLabel, QFrame, 
                             QMessageBox, QGroupBox, QProgressBar, QFileDialog,
                             QTextEdit, QListWidget, QListWidgetItem, QSplitter)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QIcon
from database import db

class SettingsWindow(QMainWindow):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إعدادات البرنامج")

        # تحديد أبعاد مناسبة للشاشات المتوسطة
        self.setFixedSize(1000, 600)

        # توسيط النافذة
        self.center_window()
        
        # إعداد التصميم
        self.setup_style()
        
        # إنشاء الواجهة
        self.setup_ui()
        
        # تحديث قائمة النسخ الاحتياطية
        self.refresh_backup_list()

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        from PyQt5.QtWidgets import QApplication
        screen = QApplication.desktop().screenGeometry()
        window = self.frameGeometry()

        # حساب الموقع المركزي
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2

        # التأكد من أن النافذة لا تخرج عن حدود الشاشة
        x = max(0, min(x, screen.width() - window.width()))
        y = max(0, min(y, screen.height() - window.height()))

        self.move(x, y)
    
    def setup_style(self):
        """إعداد تصميم النافذة"""
        self.colors = {
            'primary': '#2C3E50',
            'secondary': '#3498DB', 
            'accent': '#E74C3C',
            'success': '#27AE60',
            'warning': '#F39C12',
            'light': '#ECF0F1',
            'dark': '#34495E',
            'white': '#FFFFFF'
        }
        
        self.setStyleSheet(f"""
            QMainWindow {{
                background: {self.colors['light']};
            }}
            
            QPushButton {{
                background: {self.colors['secondary']};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 15px 25px;
                font-weight: bold;
                font-family: 'Tahoma';
                min-height: 40px;
                font-size: 14px;
            }}
            
            QPushButton:hover {{
                background: {self.colors['accent']};
            }}
            
            QPushButton:pressed {{
                background: {self.colors['dark']};
            }}
            
            QGroupBox {{
                font-weight: bold;
                font-family: 'Tahoma';
                border: 2px solid {self.colors['light']};
                border-radius: 8px;
                margin-top: 15px;
                padding-top: 15px;
                font-size: 14px;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: {self.colors['primary']};
                font-size: 16px;
            }}
            
            QLabel {{
                font-family: 'Tahoma';
                font-size: 12px;
                color: {self.colors['dark']};
            }}
            
            QProgressBar {{
                border: 2px solid {self.colors['light']};
                border-radius: 5px;
                text-align: center;
                font-family: 'Tahoma';
                font-weight: bold;
            }}
            
            QProgressBar::chunk {{
                background-color: {self.colors['success']};
                border-radius: 3px;
            }}
            
            QListWidget {{
                background: {self.colors['white']};
                border: 2px solid {self.colors['light']};
                border-radius: 8px;
                font-family: 'Tahoma';
                padding: 5px;
            }}
            
            QListWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {self.colors['light']};
                border-radius: 4px;
                margin: 2px;
            }}
            
            QListWidget::item:selected {{
                background: {self.colors['secondary']};
                color: white;
            }}
            
            QListWidget::item:hover {{
                background: {self.colors['light']};
            }}
            
            QTextEdit {{
                background: {self.colors['white']};
                border: 2px solid {self.colors['light']};
                border-radius: 8px;
                font-family: 'Tahoma';
                padding: 10px;
            }}
        """)
    
    def setup_ui(self):
        """إنشاء واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)
        
        # إنشاء الهيدر
        header = self.create_header()
        main_layout.addWidget(header)
        
        # إنشاء منطقة الإعدادات
        settings_area = self.create_settings_area()
        main_layout.addWidget(settings_area)
        
        # إنشاء شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # إنشاء أزرار التحكم
        control_buttons = self.create_control_buttons()
        main_layout.addWidget(control_buttons)
    
    def create_header(self):
        """إنشاء منطقة الهيدر"""
        header_frame = QFrame()
        header_frame.setFixedHeight(100)
        header_frame.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {self.colors['primary']}, stop:1 {self.colors['secondary']});
                border-radius: 15px;
            }}
        """)
        
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(30, 15, 30, 15)
        
        # أيقونة ونص الهيدر
        icon_label = QLabel("⚙️")
        icon_label.setFont(QFont("Arial", 40))
        icon_label.setStyleSheet("color: white; background: transparent;")
        
        title_label = QLabel("إعدادات البرنامج")
        title_label.setFont(QFont("Tahoma", 24, QFont.Bold))
        title_label.setStyleSheet("color: white; background: transparent;")
        
        subtitle_label = QLabel("إدارة النسخ الاحتياطي والإعدادات العامة")
        subtitle_label.setFont(QFont("Tahoma", 14))
        subtitle_label.setStyleSheet("color: #ECF0F1; background: transparent;")
        
        text_layout = QVBoxLayout()
        text_layout.addWidget(title_label)
        text_layout.addWidget(subtitle_label)
        
        header_layout.addWidget(icon_label)
        header_layout.addLayout(text_layout)
        header_layout.addStretch()
        
        return header_frame
    
    def create_settings_area(self):
        """إنشاء منطقة الإعدادات"""
        settings_frame = QFrame()
        settings_layout = QHBoxLayout(settings_frame)
        
        # الجانب الأيسر - إدارة النسخ الاحتياطي
        backup_group = QGroupBox("إدارة النسخ الاحتياطي")
        backup_layout = QVBoxLayout(backup_group)
        
        # أزرار النسخ الاحتياطي
        backup_buttons_layout = QVBoxLayout()
        
        create_backup_btn = QPushButton("💾 إنشاء نسخة احتياطية")
        create_backup_btn.clicked.connect(self.create_backup)
        create_backup_btn.setStyleSheet(f"background: {self.colors['success']};")
        backup_buttons_layout.addWidget(create_backup_btn)
        
        restore_backup_btn = QPushButton("📥 استرجاع نسخة احتياطية")
        restore_backup_btn.clicked.connect(self.restore_backup)
        restore_backup_btn.setStyleSheet(f"background: {self.colors['warning']};")
        backup_buttons_layout.addWidget(restore_backup_btn)
        
        auto_backup_btn = QPushButton("🔄 نسخة احتياطية تلقائية")
        auto_backup_btn.clicked.connect(self.setup_auto_backup)
        backup_buttons_layout.addWidget(auto_backup_btn)
        
        backup_layout.addLayout(backup_buttons_layout)
        
        # قائمة النسخ الاحتياطية
        backup_list_label = QLabel("النسخ الاحتياطية المتاحة:")
        backup_list_label.setFont(QFont("Tahoma", 12, QFont.Bold))
        backup_layout.addWidget(backup_list_label)
        
        self.backup_list = QListWidget()
        backup_layout.addWidget(self.backup_list)
        
        # أزرار إدارة النسخ
        backup_manage_layout = QHBoxLayout()
        
        refresh_list_btn = QPushButton("🔄 تحديث القائمة")
        refresh_list_btn.clicked.connect(self.refresh_backup_list)
        backup_manage_layout.addWidget(refresh_list_btn)
        
        delete_backup_btn = QPushButton("🗑️ حذف نسخة")
        delete_backup_btn.clicked.connect(self.delete_backup)
        delete_backup_btn.setStyleSheet(f"background: {self.colors['accent']};")
        backup_manage_layout.addWidget(delete_backup_btn)
        
        backup_layout.addLayout(backup_manage_layout)
        
        # الجانب الأيمن - إعدادات البرنامج
        program_group = QGroupBox("إعدادات البرنامج")
        program_layout = QVBoxLayout(program_group)
        
        # أزرار إعدادات البرنامج
        program_buttons_layout = QVBoxLayout()
        
        reset_program_btn = QPushButton("🔄 إعادة ضبط البرنامج")
        reset_program_btn.clicked.connect(self.reset_program)
        reset_program_btn.setStyleSheet(f"background: {self.colors['accent']};")
        program_buttons_layout.addWidget(reset_program_btn)
        
        export_data_btn = QPushButton("📤 تصدير البيانات")
        export_data_btn.clicked.connect(self.export_data)
        program_buttons_layout.addWidget(export_data_btn)
        
        import_data_btn = QPushButton("📥 استيراد البيانات")
        import_data_btn.clicked.connect(self.import_data)
        program_buttons_layout.addWidget(import_data_btn)
        
        check_integrity_btn = QPushButton("🔍 فحص سلامة البيانات")
        check_integrity_btn.clicked.connect(self.check_data_integrity)
        check_integrity_btn.setStyleSheet(f"background: {self.colors['warning']};")
        program_buttons_layout.addWidget(check_integrity_btn)
        
        program_layout.addLayout(program_buttons_layout)
        
        # معلومات البرنامج
        info_label = QLabel("معلومات البرنامج:")
        info_label.setFont(QFont("Tahoma", 12, QFont.Bold))
        program_layout.addWidget(info_label)
        
        self.info_text = QTextEdit()
        self.info_text.setReadOnly(True)
        self.info_text.setMaximumHeight(150)
        self.update_program_info()
        program_layout.addWidget(self.info_text)
        
        # إضافة المجموعات للتخطيط الرئيسي
        splitter = QSplitter(Qt.Horizontal)
        splitter.addWidget(backup_group)
        splitter.addWidget(program_group)
        splitter.setSizes([500, 400])
        
        settings_layout.addWidget(splitter)
        
        return settings_frame

    def create_control_buttons(self):
        """إنشاء أزرار التحكم السفلية"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)

        # زر فتح مجلد النسخ الاحتياطية
        open_backup_folder_btn = QPushButton("📁 فتح مجلد النسخ الاحتياطية")
        open_backup_folder_btn.clicked.connect(self.open_backup_folder)
        open_backup_folder_btn.setStyleSheet(f"background: {self.colors['success']};")

        # زر العودة
        back_btn = QPushButton("🔙 العودة للقائمة الرئيسية")
        back_btn.clicked.connect(self.close)
        back_btn.setStyleSheet(f"background: {self.colors['dark']};")

        buttons_layout.addStretch()
        buttons_layout.addWidget(open_backup_folder_btn)
        buttons_layout.addWidget(back_btn)

        return buttons_frame

    def show_progress(self, message="جاري المعالجة..."):
        """عرض شريط التقدم"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # شريط تقدم غير محدد
        self.progress_bar.setFormat(message)

    def hide_progress(self):
        """إخفاء شريط التقدم"""
        self.progress_bar.setVisible(False)

    def update_program_info(self):
        """تحديث معلومات البرنامج"""
        try:
            # جلب إحصائيات قاعدة البيانات
            suppliers_count = db.execute_query("SELECT COUNT(*) as count FROM suppliers")[0]['count']
            materials_count = db.execute_query("SELECT COUNT(*) as count FROM raw_materials")[0]['count']
            customers_count = db.execute_query("SELECT COUNT(*) as count FROM customers")[0]['count']
            products_count = db.execute_query("SELECT COUNT(*) as count FROM products")[0]['count']
            sales_count = db.execute_query("SELECT COUNT(*) as count FROM sales_invoices")[0]['count']
            purchases_count = db.execute_query("SELECT COUNT(*) as count FROM purchase_invoices")[0]['count']

            # حجم قاعدة البيانات
            db_size = 0
            if os.path.exists("database/sweets_factory.db"):
                db_size = os.path.getsize("database/sweets_factory.db") / 1024  # بالكيلوبايت

            info_text = f"""
برنامج محاسبة مصنع الحلويات التقليدية
الإصدار: 1.0
تاريخ الإنشاء: 2025-06-18
المطور: Augment Agent

إحصائيات قاعدة البيانات:
• عدد الموردين: {suppliers_count}
• عدد المواد الأولية: {materials_count}
• عدد الزبائن: {customers_count}
• عدد المنتجات: {products_count}
• عدد فواتير المبيعات: {sales_count}
• عدد فواتير المشتريات: {purchases_count}

حجم قاعدة البيانات: {db_size:.2f} كيلوبايت
مسار قاعدة البيانات: database/sweets_factory.db
            """

            self.info_text.setPlainText(info_text.strip())

        except Exception as e:
            self.info_text.setPlainText(f"خطأ في جلب معلومات البرنامج: {str(e)}")

    def refresh_backup_list(self):
        """تحديث قائمة النسخ الاحتياطية"""
        self.backup_list.clear()

        backup_folder = "backup"
        if not os.path.exists(backup_folder):
            os.makedirs(backup_folder)
            return

        try:
            backup_files = [f for f in os.listdir(backup_folder) if f.endswith('.db')]
            backup_files.sort(reverse=True)  # ترتيب حسب الأحدث

            for backup_file in backup_files:
                file_path = os.path.join(backup_folder, backup_file)
                file_size = os.path.getsize(file_path) / 1024  # بالكيلوبايت
                file_time = datetime.fromtimestamp(os.path.getmtime(file_path))

                item_text = f"{backup_file}\nالحجم: {file_size:.2f} كيلوبايت\nالتاريخ: {file_time.strftime('%Y-%m-%d %H:%M:%S')}"

                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, backup_file)
                self.backup_list.addItem(item)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحديث قائمة النسخ الاحتياطية: {str(e)}")

    def open_backup_folder(self):
        """فتح مجلد النسخ الاحتياطية"""
        backup_folder = "backup"
        if not os.path.exists(backup_folder):
            os.makedirs(backup_folder)

        try:
            os.startfile(backup_folder)  # Windows
        except:
            try:
                os.system(f"open {backup_folder}")  # macOS
            except:
                os.system(f"xdg-open {backup_folder}")  # Linux

    # دوال إدارة النسخ الاحتياطي
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        self.show_progress("جاري إنشاء النسخة الاحتياطية...")

        try:
            # إنشاء مجلد النسخ الاحتياطية
            backup_folder = "backup"
            os.makedirs(backup_folder, exist_ok=True)

            # إنشاء اسم الملف
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"backup_{timestamp}.db"
            backup_path = os.path.join(backup_folder, backup_filename)

            # نسخ قاعدة البيانات
            if db.backup_database(backup_path):
                QMessageBox.information(self, "تم", f"تم إنشاء النسخة الاحتياطية بنجاح\n{backup_path}")
                self.refresh_backup_list()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في إنشاء النسخة الاحتياطية")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}")

        finally:
            self.hide_progress()

    def restore_backup(self):
        """استرجاع نسخة احتياطية"""
        current_item = self.backup_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار نسخة احتياطية للاسترجاع")
            return

        backup_filename = current_item.data(Qt.UserRole)
        backup_path = os.path.join("backup", backup_filename)

        reply = QMessageBox.question(
            self, "تأكيد الاسترجاع",
            f"هل أنت متأكد من استرجاع النسخة الاحتياطية؟\n{backup_filename}\n\nسيتم استبدال جميع البيانات الحالية!",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.show_progress("جاري استرجاع النسخة الاحتياطية...")

            try:
                if db.restore_database(backup_path):
                    QMessageBox.information(self, "تم", "تم استرجاع النسخة الاحتياطية بنجاح\nيرجى إعادة تشغيل البرنامج")
                    self.update_program_info()
                else:
                    QMessageBox.critical(self, "خطأ", "فشل في استرجاع النسخة الاحتياطية")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في استرجاع النسخة الاحتياطية: {str(e)}")

            finally:
                self.hide_progress()

    def delete_backup(self):
        """حذف نسخة احتياطية"""
        current_item = self.backup_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار نسخة احتياطية للحذف")
            return

        backup_filename = current_item.data(Qt.UserRole)
        backup_path = os.path.join("backup", backup_filename)

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف النسخة الاحتياطية؟\n{backup_filename}",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                os.remove(backup_path)
                QMessageBox.information(self, "تم", "تم حذف النسخة الاحتياطية بنجاح")
                self.refresh_backup_list()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في حذف النسخة الاحتياطية: {str(e)}")

    def setup_auto_backup(self):
        """إعداد النسخ الاحتياطي التلقائي"""
        QMessageBox.information(self, "قريباً", "سيتم إضافة ميزة النسخ الاحتياطي التلقائي قريباً")

    # دوال إعدادات البرنامج
    def reset_program(self):
        """إعادة ضبط البرنامج"""
        reply = QMessageBox.question(
            self, "تأكيد إعادة الضبط",
            "هل أنت متأكد من إعادة ضبط البرنامج؟\n\nسيتم حذف جميع البيانات نهائياً!\nيُنصح بإنشاء نسخة احتياطية أولاً.",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # تأكيد إضافي
            confirm_reply = QMessageBox.question(
                self, "تأكيد نهائي",
                "هذا تأكيد نهائي!\n\nسيتم حذف جميع البيانات نهائياً ولا يمكن التراجع عن هذا الإجراء.\n\nهل تريد المتابعة؟",
                QMessageBox.Yes | QMessageBox.No
            )

            if confirm_reply == QMessageBox.Yes:
                self.show_progress("جاري إعادة ضبط البرنامج...")

                try:
                    if db.reset_database():
                        QMessageBox.information(self, "تم", "تم إعادة ضبط البرنامج بنجاح\nيرجى إعادة تشغيل البرنامج")
                        self.update_program_info()
                    else:
                        QMessageBox.critical(self, "خطأ", "فشل في إعادة ضبط البرنامج")

                except Exception as e:
                    QMessageBox.critical(self, "خطأ", f"خطأ في إعادة ضبط البرنامج: {str(e)}")

                finally:
                    self.hide_progress()

    def export_data(self):
        """تصدير البيانات"""
        QMessageBox.information(self, "قريباً", "سيتم إضافة ميزة تصدير البيانات قريباً")

    def import_data(self):
        """استيراد البيانات"""
        QMessageBox.information(self, "قريباً", "سيتم إضافة ميزة استيراد البيانات قريباً")

    def check_data_integrity(self):
        """فحص سلامة البيانات"""
        self.show_progress("جاري فحص سلامة البيانات...")

        try:
            # فحص أساسي لقاعدة البيانات
            issues = []

            # فحص الجداول الأساسية
            tables = ['suppliers', 'raw_materials', 'customers', 'products', 'recipes']
            for table in tables:
                try:
                    db.execute_query(f"SELECT COUNT(*) FROM {table}")
                except Exception as e:
                    issues.append(f"مشكلة في جدول {table}: {str(e)}")

            if not issues:
                QMessageBox.information(self, "فحص البيانات", "تم فحص البيانات بنجاح\nلا توجد مشاكل في قاعدة البيانات")
            else:
                issues_text = "\n".join(issues)
                QMessageBox.warning(self, "مشاكل في البيانات", f"تم العثور على المشاكل التالية:\n\n{issues_text}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فحص البيانات: {str(e)}")

        finally:
            self.hide_progress()
