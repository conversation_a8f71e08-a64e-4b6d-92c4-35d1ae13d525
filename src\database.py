#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة قاعدة البيانات
إدارة قاعدة بيانات SQLite للبرنامج
"""

import sqlite3
import os
from datetime import datetime

class Database:
    def __init__(self, db_path="database/sweets_factory.db"):
        """تهيئة قاعدة البيانات"""
        self.db_path = db_path
        
        # إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # إنشاء الاتصال وإنشاء الجداول
        self.create_tables()
    
    def get_connection(self):
        """إنشاء اتصال جديد بقاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
        return conn
    
    def create_tables(self):
        """إنشاء جميع الجداول المطلوبة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # جدول الموردين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                address TEXT,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المواد الأولية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS raw_materials (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                unit TEXT NOT NULL,
                price REAL DEFAULT 0,
                stock_quantity REAL DEFAULT 0,
                min_stock REAL DEFAULT 0,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الوصفات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS recipes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                batch_weight REAL NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول مكونات الوصفات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS recipe_ingredients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                recipe_id INTEGER NOT NULL,
                material_id INTEGER NOT NULL,
                quantity REAL NOT NULL,
                FOREIGN KEY (recipe_id) REFERENCES recipes (id),
                FOREIGN KEY (material_id) REFERENCES raw_materials (id)
            )
        ''')
        
        # جدول المنتجات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                recipe_id INTEGER,
                weight REAL NOT NULL,
                packaging_type TEXT,
                is_returnable BOOLEAN DEFAULT 0,
                price REAL DEFAULT 0,
                stock_quantity REAL DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (recipe_id) REFERENCES recipes (id)
            )
        ''')
        
        # جدول الزبائن
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                address TEXT,
                special_price_discount REAL DEFAULT 0,
                trays_with_customer INTEGER DEFAULT 0,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول العمال
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS workers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                worker_type TEXT NOT NULL CHECK (worker_type IN ('daily', 'monthly')),
                daily_rate REAL DEFAULT 0,
                monthly_salary REAL DEFAULT 0,
                phone TEXT,
                address TEXT,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول فواتير المشتريات المحسن
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS purchase_invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                supplier_id INTEGER NOT NULL,
                invoice_number TEXT,
                total_amount REAL NOT NULL,
                paid_amount REAL DEFAULT 0,
                invoice_date DATE NOT NULL,
                status TEXT DEFAULT 'مسودة',
                payment_method TEXT DEFAULT 'نقداً',
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
            )
        ''')

        # جدول تفاصيل فواتير المشتريات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS purchase_invoice_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_id INTEGER NOT NULL,
                material_id INTEGER NOT NULL,
                quantity REAL NOT NULL,
                unit_price REAL NOT NULL,
                total_price REAL NOT NULL,
                FOREIGN KEY (invoice_id) REFERENCES purchase_invoices (id),
                FOREIGN KEY (material_id) REFERENCES raw_materials (id)
            )
        ''')

        # جدول الإنتاج اليومي
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS daily_production (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                production_date DATE NOT NULL,
                recipe_id INTEGER NOT NULL,
                batches_count INTEGER NOT NULL,
                total_weight REAL NOT NULL,
                honey_distributed REAL DEFAULT 0,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (recipe_id) REFERENCES recipes (id)
            )
        ''')

        # جدول فواتير المبيعات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sales_invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id INTEGER NOT NULL,
                invoice_number TEXT,
                total_amount REAL NOT NULL,
                paid_amount REAL DEFAULT 0,
                trays_taken INTEGER DEFAULT 0,
                invoice_date DATE NOT NULL,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        ''')

        # جدول تفاصيل فواتير المبيعات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sales_invoice_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity REAL NOT NULL,
                unit_price REAL NOT NULL,
                total_price REAL NOT NULL,
                FOREIGN KEY (invoice_id) REFERENCES sales_invoices (id),
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        ''')

        # جدول الطلبيات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id INTEGER NOT NULL,
                order_date DATE NOT NULL,
                delivery_date DATE,
                status TEXT DEFAULT 'pending',
                total_amount REAL DEFAULT 0,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        ''')

        # جدول تفاصيل الطلبيات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS order_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity REAL NOT NULL,
                unit_price REAL NOT NULL,
                total_price REAL NOT NULL,
                FOREIGN KEY (order_id) REFERENCES orders (id),
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        ''')

        # جدول سلفات العمال
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS worker_advances (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                worker_id INTEGER NOT NULL,
                amount REAL NOT NULL,
                advance_date DATE NOT NULL,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (worker_id) REFERENCES workers (id)
            )
        ''')

        # جدول حضور العمال اليوميين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS daily_attendance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                worker_id INTEGER NOT NULL,
                work_date DATE NOT NULL,
                batches_worked INTEGER NOT NULL,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (worker_id) REFERENCES workers (id)
            )
        ''')

        # جدول أدوات التغليف
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS packaging_tools (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                total_quantity INTEGER DEFAULT 0,
                with_customers INTEGER DEFAULT 0,
                available_quantity INTEGER DEFAULT 0,
                is_returnable BOOLEAN DEFAULT 1,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # جدول حركة أدوات التغليف
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS packaging_movements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                tool_id INTEGER NOT NULL,
                customer_id INTEGER,
                movement_type TEXT NOT NULL CHECK (movement_type IN ('out', 'in')),
                quantity INTEGER NOT NULL,
                movement_date DATE NOT NULL,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (tool_id) REFERENCES packaging_tools (id),
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        ''')

        conn.commit()
        conn.close()

    def execute_query(self, query, params=None):
        """تنفيذ استعلام وإرجاع النتائج"""
        conn = self.get_connection()
        cursor = conn.cursor()

        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)

        results = cursor.fetchall()
        conn.close()
        return results

    def execute_insert(self, query, params=None):
        """تنفيذ استعلام إدراج وإرجاع ID الصف الجديد"""
        conn = self.get_connection()
        cursor = conn.cursor()

        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)

        last_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return last_id

    def execute_update(self, query, params=None):
        """تنفيذ استعلام تحديث وإرجاع عدد الصفوف المتأثرة"""
        conn = self.get_connection()
        cursor = conn.cursor()

        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)

        rows_affected = cursor.rowcount
        conn.commit()
        conn.close()
        return rows_affected

    def backup_database(self, backup_path):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            import shutil
            shutil.copy2(self.db_path, backup_path)
            return True
        except Exception as e:
            print(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False

    def restore_database(self, backup_path):
        """استرجاع قاعدة البيانات من نسخة احتياطية"""
        try:
            import shutil
            shutil.copy2(backup_path, self.db_path)
            return True
        except Exception as e:
            print(f"خطأ في استرجاع النسخة الاحتياطية: {e}")
            return False

    def reset_database(self):
        """إعادة ضبط قاعدة البيانات (حذف جميع البيانات)"""
        try:
            if os.path.exists(self.db_path):
                os.remove(self.db_path)
            self.create_tables()
            return True
        except Exception as e:
            print(f"خطأ في إعادة ضبط قاعدة البيانات: {e}")
            return False

# إنشاء مثيل عام من قاعدة البيانات
db = Database()
