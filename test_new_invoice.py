#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نافذة الفاتورة المحدثة مع تفاصيل المواد
"""

import sys
import os
sys.path.append('src')

def setup_test_data():
    """إعداد بيانات تجريبية للاختبار"""
    print("🔄 إعداد بيانات تجريبية...")
    
    try:
        from database import db
        
        # إضافة مورد تجريبي
        supplier_id = db.execute_insert("""
            INSERT INTO suppliers (name, phone, address, notes)
            VALUES (?, ?, ?, ?)
        """, ("شركة المواد الغذائية", "0501234567", "الرياض - حي الملز", "مورد رئيسي"))
        
        print(f"✅ تم إضافة مورد تجريبي برقم: {supplier_id}")
        
        # إضافة مواد أولية تجريبية
        materials = [
            ("دقيق أبيض فاخر", "كيلوغرام", 3.50, 0.0, 10.0, "دقيق للحلويات"),
            ("سكر أبيض ناعم", "كيلوغرام", 2.80, 0.0, 20.0, "سكر عالي الجودة"),
            ("زبدة طبيعية", "كيلوغرام", 25.00, 0.0, 5.0, "زبدة بقري طازجة"),
            ("عسل طبيعي", "كيلوغرام", 45.00, 0.0, 3.0, "عسل سدر أصلي"),
            ("لوز مقشر", "كيلوغرام", 35.00, 0.0, 2.0, "لوز كاليفورنيا"),
        ]
        
        for material in materials:
            material_id = db.execute_insert("""
                INSERT INTO raw_materials (name, unit, price, stock_quantity, min_stock, notes)
                VALUES (?, ?, ?, ?, ?, ?)
            """, material)
            print(f"✅ تم إضافة مادة: {material[0]} برقم: {material_id}")
        
        print("✅ تم إعداد البيانات التجريبية بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعداد البيانات: {str(e)}")
        return False

def test_invoice_dialog():
    """اختبار نافذة الفاتورة الجديدة"""
    print("🖥️ اختبار نافذة الفاتورة الجديدة...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from purchases_window import InvoiceDialog
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء نافذة الفاتورة
        dialog = InvoiceDialog()
        dialog.show()
        
        print("✅ تم فتح نافذة الفاتورة الجديدة بنجاح")
        print("\n📝 يمكنك الآن اختبار:")
        print("   1. اختيار المورد")
        print("   2. إدخال رقم الفاتورة")
        print("   3. إضافة المواد:")
        print("      - اختر المادة")
        print("      - أدخل الكمية")
        print("      - تحقق من السعر (يتم تحديثه تلقائياً)")
        print("      - اضغط 'إضافة المادة'")
        print("   4. تحقق من حساب المجموع تلقائياً")
        print("   5. أدخل المبلغ المدفوع")
        print("   6. اضغط 'حفظ الفاتورة'")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نافذة الفاتورة: {str(e)}")
        import traceback
        traceback.print_exc()

def test_full_purchases_window():
    """اختبار نافذة المشتريات الكاملة"""
    print("🖥️ اختبار نافذة المشتريات الكاملة...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from purchases_window import PurchasesWindow
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء نافذة المشتريات
        window = PurchasesWindow()
        window.show()
        
        print("✅ تم فتح نافذة المشتريات بنجاح")
        print("\n📝 للاختبار:")
        print("   1. انتقل لتبويب 'فواتير المشتريات'")
        print("   2. اضغط 'إضافة فاتورة جديدة'")
        print("   3. جرب النافذة الجديدة مع تفاصيل المواد")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نافذة المشتريات: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار نافذة الفاتورة المحدثة")
    print("=" * 60)
    
    # إعداد البيانات التجريبية
    if not setup_test_data():
        print("❌ فشل في إعداد البيانات التجريبية")
        return
    
    print("\n" + "=" * 40)
    print("اختر طريقة الاختبار:")
    print("1. اختبار نافذة الفاتورة فقط")
    print("2. اختبار نافذة المشتريات الكاملة")
    print("=" * 40)
    
    choice = input("أدخل اختيارك (1 أو 2): ").strip()
    
    if choice == "1":
        test_invoice_dialog()
    elif choice == "2":
        test_full_purchases_window()
    else:
        print("اختيار غير صحيح. سيتم اختبار النافذة الكاملة...")
        test_full_purchases_window()

if __name__ == "__main__":
    main()
