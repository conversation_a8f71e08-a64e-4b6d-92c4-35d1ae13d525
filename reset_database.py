#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعادة تعيين قاعدة البيانات لحالة نظيفة
حذف جميع البيانات التجريبية
"""

import sys
import os
import sqlite3
from datetime import datetime
import shutil

def reset_database():
    """إعادة تعيين قاعدة البيانات"""
    
    print("🔄 بدء عملية إعادة تعيين قاعدة البيانات...")
    
    try:
        # مسار قاعدة البيانات
        db_path = "database/sweets_factory.db"
        
        # إنشاء نسخة احتياطية أولاً
        if os.path.exists(db_path):
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"backup/backup_before_reset_{timestamp}.db"
            os.makedirs("backup", exist_ok=True)
            shutil.copy2(db_path, backup_path)
            print(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
        
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # قائمة الجداول للحذف
        tables = [
            'daily_production',
            'sales_invoice_items', 
            'purchase_invoice_items',
            'recipe_ingredients',
            'worker_advances',
            'daily_attendance',
            'sales_invoices',
            'purchase_invoices',
            'packaging_tools',
            'products',
            'recipes',
            'customers',
            'workers',
            'raw_materials',
            'suppliers'
        ]
        
        # حذف البيانات من كل جدول
        for table in tables:
            try:
                cursor.execute(f"DELETE FROM {table}")
                cursor.execute(f"DELETE FROM sqlite_sequence WHERE name='{table}'")
                print(f"   ✅ تم حذف بيانات جدول {table}")
            except Exception as e:
                print(f"   ⚠️ تحذير في جدول {table}: {str(e)}")
        
        # حفظ التغييرات
        conn.commit()
        
        # عرض إحصائيات بعد الحذف
        print("\n📊 إحصائيات قاعدة البيانات بعد الحذف:")
        for table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   • {table}: {count} سجل")
            except:
                pass
        
        # إغلاق الاتصال
        conn.close()
        
        print("\n✅ تم إعادة تعيين قاعدة البيانات بنجاح!")
        print("📝 البرنامج جاهز الآن للاستخدام مع بيانات حقيقية")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعادة تعيين قاعدة البيانات: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("=" * 50)
    print("🗑️ إعادة تعيين قاعدة البيانات")
    print("🏭 برنامج محاسبة مصنع الحلويات")
    print("=" * 50)
    
    if reset_database():
        print("\n🎉 تمت العملية بنجاح!")
        print("يمكنك الآن تشغيل البرنامج وإدخال بياناتك الحقيقية.")
    else:
        print("\n❌ فشلت العملية!")

if __name__ == "__main__":
    main()
