#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نافذة الفاتورة المصلحة
"""

import sys
import os
sys.path.append('src')

def test_invoice_window():
    """اختبار نافذة الفاتورة المصلحة"""
    print("🧪 اختبار نافذة الفاتورة المصلحة...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from purchases_window import InvoiceDialog
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء نافذة الفاتورة
        dialog = InvoiceDialog()
        
        print("✅ تم إنشاء نافذة الفاتورة")
        print(f"📏 حجم النافذة: {dialog.width()}×{dialog.height()}")
        
        # عرض النافذة
        dialog.show()
        
        print("✅ تم عرض النافذة")
        print("\n📋 تحقق من:")
        print("   ✅ ظهور جميع الخانات والحقول بوضوح")
        print("   ✅ وضوح النصوص والتسميات")
        print("   ✅ ظهور أزرار الحفظ والإلغاء")
        print("   ✅ رقم الفاتورة التلقائي")
        print("   ✅ العملة بالدينار الجزائري (دج)")
        print("   ✅ تناسق التخطيط والعناصر")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النافذة: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_invoice_window()
